import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shelf_static/shelf_static.dart';
import 'package:path/path.dart' as p;

class MeetingControl extends StatefulWidget {
  const MeetingControl({super.key});

  @override
  State<MeetingControl> createState() => _WebviewAppState();
}

class _WebviewAppState extends State<MeetingControl> {
  late final InAppWebViewController _webViewController;
  late final HttpServer _server;
  String? _serverUrl;

  Future<void> _createNomediaFileIfNotExists() async {
    Directory dir = await getApplicationDocumentsDirectory();
    String nomediaPath = '${dir.path}/.nomedia';
    File nomediaFile = File(nomediaPath);
    if (!await nomediaFile.exists()) await nomediaFile.create();
  }

  Future<void> _copyAssetsToDirectory(Directory targetDir) async {
    // 创建目标目录
    await targetDir.create(recursive: true);

    // 加载 AssetManifest.json，它包含了所有 asset 的路径列表
    final manifestContent = await rootBundle.loadString('AssetManifest.json');
    final Map<String, dynamic> manifestMap = json.decode(manifestContent);

    // 遍历所有 asset
    final assetKeys =
        manifestMap.keys.where((String key) => key.startsWith('assets/'));

    for (final assetKey in assetKeys) {
      final assetData = await rootBundle.load(assetKey);

      // 创建目标文件路径，并确保子目录存在
      final targetFile =
          File(p.join(targetDir.path, assetKey.replaceFirst('assets/', '')));
      await targetFile.parent.create(recursive: true);

      // 将 asset 数据写入文件
      await targetFile.writeAsBytes(assetData.buffer.asUint8List());
    }
  }

  Future initWebView() async {
    await _createNomediaFileIfNotExists();

    // _webViewController.loadFile(assetFilePath: (await getApplicationDocumentsDirectory()).path + "/Test.html");

    final tempDir = await getTemporaryDirectory();
    final assetsDir = Directory(p.join(tempDir.path, 'assets'));

    if (!await assetsDir.exists()) {
      await _copyAssetsToDirectory(assetsDir);
    }

    _server = await shelf_io.serve(
        createStaticHandler(assetsDir.path,
            listDirectories: true, defaultDocument: 'index.html'),
        '127.0.0.1',
        44325);
    //     .then((server) {
    //   return server;
    // });
    setState(() {
      _serverUrl = 'http://${_server.address.host}:${_server.port}';
    });
    if (_webViewController != null) {
      await _webViewController.loadUrl(
        urlRequest: URLRequest(url: WebUri(_serverUrl!)),
      );
    }
  }

  @override
  void initState() {
    //SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    super.initState();
    initWebView();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.bottom]);
    return MaterialApp(
        debugShowCheckedModeBanner: false,
        home: WillPopScope(
            onWillPop: () async {
              dispose();
              return Future.value(true);
            },
            child: Scaffold(
                body: Column(
              children: [
                Expanded(
                    child: InAppWebView(
                        //initialFile: 'assets/www/Test1.html',
                        //initialFile: 'assets/www/MyFriends.html',
                        //initialUrlRequest: URLRequest(url: WebUri("https://172.16.22.128:8543")),
                        //initialUrlRequest: URLRequest(url: WebUri("http://127.0.0.1:44325/")),
                        initialSettings: InAppWebViewSettings(
                          mediaPlaybackRequiresUserGesture: false,
                          allowsInlineMediaPlayback: true,
                          // iframeAllow: "camera; microphone",
                          // iframeAllowFullscreen: true
                        ),
                        onWebViewCreated: (controller) async {
                          _webViewController = controller;
                          //_webViewController.loadFile(assetFilePath: "assets/www/Test.html");
                          //await _loadLocalHtmlWithCustomSsl();

                          // _webViewController.addJavaScriptHandler(
                          //   handlerName: 'flutterToJsChannel',
                          //   callback: (args) async {
                          //     final message = args[0];
                          //     if (message == "dbr") {}
                          //   },
                          // );
                        },
                        // androidOnPermissionRequest: (controller, origin, resources) async {
                        //   return PermissionRequestResponse(resources: resources, action: PermissionRequestResponseAction.GRANT);
                        // },
                        // onPermissionRequest: (controller, request) async {
                        //   return PermissionResponse(resources: request.resources, action: PermissionResponseAction.GRANT);
                        // },
                        onPermissionRequest: (controller, request) async {
                          final resources = <PermissionResourceType>[];
                          if (request.resources
                              .contains(PermissionResourceType.CAMERA)) {
                            final cameraStatus =
                                await Permission.camera.request();
                            if (!cameraStatus.isDenied) {
                              resources.add(PermissionResourceType.CAMERA);
                            }
                          }
                          if (request.resources
                              .contains(PermissionResourceType.MICROPHONE)) {
                            final microphoneStatus =
                                await Permission.microphone.request();
                            if (!microphoneStatus.isDenied) {
                              resources.add(PermissionResourceType.MICROPHONE);
                            }
                          }
                          // only for iOS and macOS
                          if (request.resources.contains(
                              PermissionResourceType.CAMERA_AND_MICROPHONE)) {
                            final cameraStatus =
                                await Permission.camera.request();
                            final microphoneStatus =
                                await Permission.microphone.request();
                            if (!cameraStatus.isDenied &&
                                !microphoneStatus.isDenied) {
                              resources.add(
                                  PermissionResourceType.CAMERA_AND_MICROPHONE);
                            }
                          }
                          return PermissionResponse(
                              resources: resources,
                              action: resources.isEmpty
                                  ? PermissionResponseAction.DENY
                                  : PermissionResponseAction.GRANT);
                        },
                        // onReceivedServerTrustAuthRequest: (controller, challenge) async {
                        //   return ServerTrustAuthResponse(action: ServerTrustAuthResponseAction.PROCEED);
                        // },
                        onReceivedHttpAuthRequest:
                            (controller, challenge) async {
                          return HttpAuthResponse(
                              action: HttpAuthResponseAction.PROCEED);
                        }))
              ],
            ))));
  }

  @override
  void dispose() {
    _server.close();
    super.dispose();
  }
}
