from sqlalchemy import (Column, Integer, String, Text, Enum, TIMESTAMP,
                        ForeignKey, JSON, BIGINT)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

# 定义任务状态的枚举类，与数据库ENUM类型对应
class TaskStatusEnum(str, enum.Enum):
    NEW = 'NEW'
    PROCESSING = 'PROCESSING'
    COMPLETED = 'COMPLETED'
    FAILED = 'FAILED'

# 主表：会议任务模型
class MeetingTask(Base):
    __tablename__ = "meeting_tasks"
    id = Column(BIGINT, primary_key=True, index=True, autoincrement=True, comment="自增主键")
    task_id = Column(String(64), unique=True, index=True, nullable=False, comment="上游API的任务ID")
    task_key = Column(String(128), index=True, comment="客户端自定义的任务标识")
    task_status = Column(Enum(TaskStatusEnum), nullable=False, default=TaskStatusEnum.NEW, comment="任务状态")
    meeting_join_url = Column(Text, comment="WebSocket连接地址")
    output_mp3_path = Column(Text, comment="会议录音MP3下载地址")
    meeting_assistance_url = Column(Text, comment="智能会议纪要地址")
    transcription_url = Column(Text, comment="语音转录结果地址")
    summarization_url = Column(Text, comment="会议摘要信息地址")
    created_at = Column(TIMESTAMP, server_default=func.now(), comment="创建时间")
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.now(), comment="最后更新时间")

    # 定义与子表的一对一关系
    transcription = relationship("Transcription", back_populates="task", uselist=False, cascade="all, delete-orphan")
    summarization = relationship("Summarization", back_populates="task", uselist=False, cascade="all, delete-orphan")
    assistance = relationship("MeetingAssistance", back_populates="task", uselist=False, cascade="all, delete-orphan")

# 子表：语音转录结果模型
class Transcription(Base):
    __tablename__ = "transcriptions"
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    task_id = Column(String(64), ForeignKey("meeting_tasks.task_id", ondelete="CASCADE"), unique=True, nullable=False)
    audio_duration_ms = Column(Integer, comment="音频总时长（毫秒）")
    paragraphs = Column(JSON, nullable=False, comment="段落与词语信息数组")
    audio_segments = Column(JSON, comment="音频有效片段的起止时间数组")
    task = relationship("MeetingTask", back_populates="transcription")

# 子表：会议摘要模型
class Summarization(Base):
    __tablename__ = "summarizations"
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    task_id = Column(String(64), ForeignKey("meeting_tasks.task_id", ondelete="CASCADE"), unique=True, nullable=False)
    paragraph_title = Column(String(255), comment="全文摘要标题")
    paragraph_summary = Column(Text, comment="全文摘要内容")
    conversational_summary = Column(JSON, comment="分角色摘要数组")
    task = relationship("MeetingTask", back_populates="summarization")

# 子表：智能会议纪要模型
class MeetingAssistance(Base):
    __tablename__ = "meeting_assistances"
    id = Column(BIGINT, primary_key=True, autoincrement=True)
    task_id = Column(String(64), ForeignKey("meeting_tasks.task_id", ondelete="CASCADE"), unique=True, nullable=False)
    keywords = Column(JSON, comment="关键词列表")
    classifications = Column(JSON, comment="会议分类及置信度")
    task = relationship("MeetingTask", back_populates="assistance")