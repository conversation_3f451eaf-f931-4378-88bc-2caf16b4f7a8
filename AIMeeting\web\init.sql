-- AI会议记录数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ai_meeting DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ai_meeting;

-- 会议任务主表
CREATE TABLE IF NOT EXISTS meeting_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '上游API的任务ID',
    task_key VARCHAR(128) COMMENT '客户端自定义的任务标识',
    task_status ENUM('NEW', 'PROCESSING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'NEW' COMMENT '任务状态',
    meeting_join_url TEXT COMMENT 'WebSocket连接地址',
    output_mp3_path TEXT COMMENT '会议录音MP3下载地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    INDEX idx_task_id (task_id),
    INDEX idx_task_key (task_key),
    INDEX idx_task_status (task_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议任务主表';

-- 语音转录结果表
CREATE TABLE IF NOT EXISTS transcriptions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '关联的任务ID',
    audio_duration_ms INT COMMENT '音频总时长（毫秒）',
    paragraphs JSON NOT NULL COMMENT '段落与词语信息数组',
    audio_segments JSON COMMENT '音频有效片段的起止时间数组',
    FOREIGN KEY (task_id) REFERENCES meeting_tasks(task_id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语音转录结果表';

-- 会议摘要表
CREATE TABLE IF NOT EXISTS summarizations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '关联的任务ID',
    paragraph_title VARCHAR(255) COMMENT '全文摘要标题',
    paragraph_summary TEXT COMMENT '全文摘要内容',
    conversational_summary JSON COMMENT '分角色摘要数组',
    FOREIGN KEY (task_id) REFERENCES meeting_tasks(task_id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议摘要表';

-- 会议助手结果表
CREATE TABLE IF NOT EXISTS meeting_assistances (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '关联的任务ID',
    keywords JSON COMMENT '关键词数组',
    classifications JSON COMMENT '分类信息对象',
    FOREIGN KEY (task_id) REFERENCES meeting_tasks(task_id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议助手结果表';

SET FOREIGN_KEY_CHECKS = 1;
