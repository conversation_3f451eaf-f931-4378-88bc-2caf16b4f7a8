<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 网络 -->
    <uses-permission
        android:name="android.permission.INTERNET"/>
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission
        android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission
        android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission
        android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission
        android:name="android.permission.NFC"/>
    <!-- 存储 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!-- 音视频 -->
    <uses-permission
        android:name="android.permission.CAMERA"/>
    <uses-permission
        android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission
        android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission
        android:name="android.permission.MICROPHONE"/>
    <uses-permission
        android:name="android.permission.AUDIO_CAPTURE"/>
    <uses-permission
        android:name="android.permission.VIDEO_CAPTURE"/>
    <uses-permission
        android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT"/>
    <uses-permission
        android:name="android.permission.CAPTURE_VIDEO_OUTPUT"/>
    <uses-permission
        android:name="android.permission.CAPTURE_AUDIO_OUTPUT"/>
    <!-- 音视频扩展 -->
    <uses-feature
        android:name="android.hardware.camera"/>
    <uses-feature
        android:name="android.hardware.camera.autofocus"/>
    <uses-feature
        android:name="android.hardware.microphone"/>
    <!-- 防止应用在后台被杀死 -->
    <uses-permission
        android:name="android.permission.WAKE_LOCK"/>
    <application
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:label="AI会议记录"
        android:usesCleartextTraffic="true">
        <!--        android:networkSecurityConfig="@xml/network_security_config">-->
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2"/>
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility?hl=en and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action
                android:name="android.intent.action.PROCESS_TEXT"/>
            <data
                android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>