from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# 创建数据库引擎
engine = create_engine(settings.DATABASE_URL)

# 创建数据库会话类
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建所有ORM模型的基类
Base = declarative_base()

# FastAPI依赖项：为每个请求提供一个数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()