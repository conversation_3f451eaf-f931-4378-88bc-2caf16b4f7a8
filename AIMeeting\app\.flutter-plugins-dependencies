{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "flutter_inappwebview_ios", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_inappwebview_ios-1.0.13\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_native_splash-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": []}], "android": [{"name": "flutter_inappwebview_android", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_inappwebview_android-1.0.13\\\\", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_native_splash-2.4.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_android-2.2.4\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\permission_handler_android-12.0.13\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "flutter_inappwebview_macos", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_inappwebview_macos-1.0.11\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "path_provider_linux", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}], "windows": [{"name": "path_provider_windows", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": []}], "web": [{"name": "flutter_inappwebview_web", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_inappwebview_web-1.0.8\\\\", "dependencies": []}, {"name": "flutter_native_splash", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\flutter_native_splash-2.4.0\\\\", "dependencies": []}, {"name": "permission_handler_html", "path": "G:\\\\Temp\\\\Flutter\\\\.pub-cache\\\\hosted\\\\pub.flutter-io.cn\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": []}]}, "dependencyGraph": [{"name": "flutter_inappwebview", "dependencies": ["flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_web"]}, {"name": "flutter_inappwebview_android", "dependencies": []}, {"name": "flutter_inappwebview_ios", "dependencies": []}, {"name": "flutter_inappwebview_macos", "dependencies": []}, {"name": "flutter_inappwebview_web", "dependencies": []}, {"name": "flutter_native_splash", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}], "date_created": "2025-07-28 23:14:34.989238", "version": "3.19.6"}