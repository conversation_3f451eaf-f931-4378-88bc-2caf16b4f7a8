import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'meeting.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  requestPermissions();
  runApp(const MeetingControl());
}

Future<void> requestPermissions() async {
  final PermissionStatus cameraPermission = await Permission.camera.request();
  final PermissionStatus microphonePermission =
      await Permission.microphone.request();
  final PermissionStatus storagePermission = await Permission.storage.request();
  if (cameraPermission != PermissionStatus.granted ||
      microphonePermission != PermissionStatus.granted ||
      storagePermission != PermissionStatus.granted) {
    openAppSettings();
  }
}
