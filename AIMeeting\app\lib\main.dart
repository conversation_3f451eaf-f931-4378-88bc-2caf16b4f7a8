import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'meeting.dart';

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  // 保持启动页显示
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // 请求权限
  await requestPermissions();

  // 启动应用
  runApp(const MeetingControl());

  // 移除启动页
  FlutterNativeSplash.remove();
}

Future<void> requestPermissions() async {
  final PermissionStatus cameraPermission = await Permission.camera.request();
  final PermissionStatus microphonePermission =
      await Permission.microphone.request();
  final PermissionStatus storagePermission = await Permission.storage.request();
  if (cameraPermission != PermissionStatus.granted ||
      microphonePermission != PermissionStatus.granted ||
      storagePermission != PermissionStatus.granted) {
    openAppSettings();
  }
}
