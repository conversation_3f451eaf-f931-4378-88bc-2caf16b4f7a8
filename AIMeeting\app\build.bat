@echo off
echo ========================================
echo AI会议记录APP - Flutter构建脚本
echo ========================================

echo.
echo 1. 检查Flutter环境...
flutter doctor -v
if %errorlevel% neq 0 (
    echo 错误: Flutter环境未正确配置
    echo 请确保Flutter SDK已安装并添加到PATH环境变量
    pause
    exit /b 1
)

echo.
echo 2. 清理项目...
flutter clean

echo.
echo 3. 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo 错误: 依赖包获取失败
    pause
    exit /b 1
)

echo.
echo 4. 检查连接的设备...
flutter devices

echo.
echo 请选择构建类型:
echo 1. Debug模式运行
echo 2. Release模式运行
echo 3. 构建Debug APK
echo 4. 构建Release APK
echo 5. 构建分架构APK
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 启动Debug模式...
    flutter run --debug
) else if "%choice%"=="2" (
    echo.
    echo 启动Release模式...
    flutter run --release
) else if "%choice%"=="3" (
    echo.
    echo 构建Debug APK...
    flutter build apk --debug
    echo APK文件位置: build\app\outputs\flutter-apk\app-debug.apk
) else if "%choice%"=="4" (
    echo.
    echo 构建Release APK...
    flutter build apk --release
    echo APK文件位置: build\app\outputs\flutter-apk\app-release.apk
) else if "%choice%"=="5" (
    echo.
    echo 构建分架构APK...
    flutter build apk --split-per-abi --release
    echo APK文件位置: build\app\outputs\flutter-apk\
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成!
echo ========================================
pause
