// AI会议记录APP - 主要JavaScript文件
// 阶段三：H5前端业务逻辑实现

// 路由管理器类
class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.params = {};
        this.state = {};
        this.initialized = false;

        // 绑定浏览器历史记录事件
        window.addEventListener('popstate', (e) => this.handlePopState(e));
    }

    // 注册路由
    register(path, handler, options = {}) {
        this.routes.set(path, {
            handler,
            title: options.title || '',
            requiresAuth: options.requiresAuth || false
        });
    }

    // 初始化路由系统
    init() {
        if (this.initialized) return;
        this.initialized = true;

        // 从URL hash获取当前路由
        const hash = window.location.hash.slice(1) || '/';
        this.navigate(hash, { replace: true });
    }

    // 导航到指定路由
    navigate(path, options = {}) {
        const { replace = false, state = {} } = options;

        // 解析路径和参数
        const { route, params } = this.parsePath(path);

        if (!this.routes.has(route)) {
            console.warn(`路由 ${route} 未找到，跳转到首页`);
            // 防止无限递归，检查是否是首页路由
            if (route !== '/' && this.routes.has('/')) {
                this.navigate('/', { replace: true });
                return;
            } else if (!this.routes.has('/')) {
                console.error('首页路由未注册，无法进行重定向');
                return;
            }
        }

        // 保存当前状态
        this.currentRoute = route;
        this.params = params;
        this.state = { ...this.state, ...state };

        // 更新浏览器历史记录
        const url = `#${path}`;
        if (replace) {
            window.history.replaceState({ route, params, state: this.state }, '', url);
        } else {
            window.history.pushState({ route, params, state: this.state }, '', url);
        }

        // 执行路由处理器
        const routeConfig = this.routes.get(route);
        try {
            routeConfig.handler(params, this.state);

            // 更新页面标题
            if (routeConfig.title) {
                document.title = routeConfig.title;
            }
        } catch (error) {
            console.error(`路由处理器执行失败: ${route}`, error);
        }
    }

    // 解析路径和参数
    parsePath(path) {
        const [routePath, queryString] = path.split('?');
        const params = {};

        // 解析查询参数
        if (queryString) {
            queryString.split('&').forEach(param => {
                const [key, value] = param.split('=');
                params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            });
        }

        // 首先检查精确匹配的路由
        if (this.routes.has(routePath)) {
            return { route: routePath, params };
        }

        // 然后解析路径参数（如 /meeting/:id）
        for (const [route] of this.routes) {
            if (route.includes(':')) {
                const routeParts = route.split('/');
                const pathParts = routePath.split('/');

                if (routeParts.length === pathParts.length) {
                    let match = true;
                    const pathParams = {};

                    for (let i = 0; i < routeParts.length; i++) {
                        if (routeParts[i].startsWith(':')) {
                            pathParams[routeParts[i].slice(1)] = pathParts[i];
                        } else if (routeParts[i] !== pathParts[i]) {
                            match = false;
                            break;
                        }
                    }

                    if (match) {
                        return { route, params: { ...params, ...pathParams } };
                    }
                }
            }
        }

        // 如果没有匹配的路由，返回原路径（这将在navigate中被处理）
        return { route: routePath, params };
    }

    // 处理浏览器前进后退
    handlePopState(event) {
        if (event.state) {
            this.currentRoute = event.state.route;
            this.params = event.state.params || {};
            this.state = event.state.state || {};

            const routeConfig = this.routes.get(this.currentRoute);
            if (routeConfig) {
                routeConfig.handler(this.params, this.state);
            }
        } else {
            // 没有状态信息，导航到首页
            const hash = window.location.hash.slice(1) || '/';
            if (this.routes.has(hash)) {
                this.navigate(hash, { replace: true });
            } else if (this.routes.has('/')) {
                this.navigate('/', { replace: true });
            }
        }
    }

    // 获取当前路由信息
    getCurrentRoute() {
        return {
            route: this.currentRoute,
            params: this.params,
            state: this.state
        };
    }

    // 更新当前路由状态
    updateState(newState) {
        this.state = { ...this.state, ...newState };
        window.history.replaceState(
            { route: this.currentRoute, params: this.params, state: this.state },
            '',
            window.location.hash
        );
    }
}

class AIMeetingApp {
    constructor() {
        // 路由管理器
        this.router = new Router();

        // 应用状态
        this.currentPage = 'home';
        this.sidebarOpen = false;
        this.isOnline = navigator.onLine;
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.meetings = [];
        this.filteredMeetings = [];
        this.meetingInProgress = false;
        this.meetingStartTime = null;
        this.timerInterval = null;
        this.wordCount = 0;

        // 页面状态管理
        this.pageStates = {
            home: {
                scrollPosition: 0,
                selectedFilter: 'all',
                searchQuery: '',
                currentPage: 0,
                hasMore: true
            },
            detail: {
                meetingId: null,
                scrollPosition: 0,
                audioCurrentTime: 0,
                pollingInterval: null,
                lastUpdateTime: null
            },
            meeting: {
                startTime: null,
                transcripts: [],
                duration: 0
            }
        };

        // 会议列表管理
        this.meetingList = {
            data: [],
            loading: false,
            hasMore: true,
            currentPage: 0,
            pageSize: 10,
            totalCount: 0
        };

        // 详情页轮询管理
        this.detailPolling = {
            interval: null,
            retryCount: 0,
            maxRetries: 3,
            pollingDelay: 30000 // 30秒
        };

        // 获取配置
        this.config = window.getConfig ? window.getConfig() : {
            api: {
                baseUrl: 'http://*************:8000/api/v1',
                authToken: '6L+H5b6X6aOe5b+r5o6g6L+H55qE77yM5byA5LqG5Liq77yM55qE5Lik5Liqdm1mbHM7bWZsbU06TE1PXiZUJjg3NzXliIbmrrXml7Y=',
                timeout: 30000
            },
            websocket: {
                maxRetryCount: 3,
                retryDelay: 2000
            }
        };

        // 会议状态管理
        this.meetingState = {
            currentTaskId: null,
            meetingJoinUrl: null,
            isRecording: false,
            websocket: null,
            audioRecorder: null,
            audioStream: null,
            transcriptBuffer: [],
            connectionRetryCount: 0,
            maxRetryCount: this.config.websocket?.maxRetryCount || 3
        };

        this.init();
    }

    init() {
        this.setupRoutes();
        this.bindEvents();
        this.checkOnlineStatus();
        this.initSidebar();

        // 强制初始化页面状态 - 确保只显示首页
        this.forceInitializePageState();

        this.loadMeetingList(); // 加载会议列表数据

        // 初始化时重置音频播放器
        this.resetAudioPlayer();

        // 在所有基础设置完成后初始化路由系统
        this.router.init();

        // 路由初始化后恢复页面状态
        setTimeout(() => {
            this.restorePageState();
            this.handlePageRefresh();
        }, 0);
    }

    // 强制初始化页面状态
    forceInitializePageState() {
        const homeContent = document.getElementById('home-content');
        const meetingContent = document.getElementById('meeting-content');
        const detailContent = document.getElementById('detail-content');

        // 确保只显示首页
        if (homeContent) homeContent.classList.remove('hidden');
        if (meetingContent) meetingContent.classList.add('hidden');
        if (detailContent) detailContent.classList.add('hidden');

        this.currentPage = 'home';
    }

    // 设置路由配置
    setupRoutes() {
        // 首页路由
        this.router.register('/', (params, state) => {
            this.showHomePage(state);
        }, { title: 'AI会议记录 - 首页' });

        // 会议详情页路由
        this.router.register('/meeting/:id', (params, state) => {
            this.showMeetingDetailPage(params.id, state);
        }, { title: 'AI会议记录 - 会议详情' });

        // 会议进行中页面路由
        this.router.register('/meeting-active', (params, state) => {
            this.showMeetingActive(state);
        }, { title: 'AI会议记录 - 会议进行中' });
    }

    // 恢复页面状态
    restorePageState() {
        const currentRoute = this.router.getCurrentRoute();

        // 根据路由状态恢复页面
        if (currentRoute.state.pageState) {
            const pageState = currentRoute.state.pageState;

            // 恢复搜索和筛选状态
            if (pageState.searchQuery) {
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.value = pageState.searchQuery;
                    this.searchQuery = pageState.searchQuery;
                }
            }

            if (pageState.selectedFilter) {
                this.currentFilter = pageState.selectedFilter;
                this.updateFilterButtons();
            }

            // 恢复滚动位置
            if (pageState.scrollPosition) {
                setTimeout(() => {
                    window.scrollTo(0, pageState.scrollPosition);
                }, 100);
            }
        }
    }

    // 保存当前页面状态
    savePageState() {
        const currentRoute = this.router.getCurrentRoute();
        const pageState = {
            scrollPosition: window.pageYOffset,
            searchQuery: this.searchQuery,
            selectedFilter: this.currentFilter,
            timestamp: Date.now()
        };

        // 根据当前页面保存特定状态
        if (this.currentPage === 'detail') {
            const audioPlayer = document.querySelector('audio');
            if (audioPlayer) {
                pageState.audioCurrentTime = audioPlayer.currentTime;
            }
        }

        this.router.updateState({ pageState });
    }

    // API调用方法
    async apiCall(endpoint, options = {}) {
        const {
            method = 'GET',
            data = null,
            timeout = this.config.api.timeout
        } = options;

        const url = `${this.config.api.baseUrl}${endpoint}`;
        const config = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': this.config.api.authToken
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            config.body = JSON.stringify(data);
        }

        // 调试日志
        console.log('API调用详情:', {
            url,
            method,
            headers: config.headers,
            body: config.body
        });

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            config.signal = controller.signal;

            const response = await fetch(url, config);
            clearTimeout(timeoutId);

            console.log('API响应状态:', response.status, response.statusText);

            // 尝试解析响应体
            let result;
            try {
                result = await response.json();
                console.log('API响应数据:', result);
            } catch (parseError) {
                console.error('解析响应JSON失败:', parseError);
                const text = await response.text();
                console.log('响应文本内容:', text);
                throw new Error(`响应解析失败: ${parseError.message}`);
            }

            if (!response.ok) {
                const errorMessage = result?.detail || result?.msg || `HTTP ${response.status}: ${response.statusText}`;
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            console.error('API调用失败:', error);
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查后端服务是否运行');
            }
            throw error;
        }
    }

    // 创建会议任务
    async createMeeting() {
        try {
            this.showNotification('正在创建会议...', 'info');

            const result = await this.apiCall('/meetings', {
                method: 'POST'
            });

            if (result.code === 200) {
                this.meetingState.currentTaskId = result.taskId;
                this.meetingState.meetingJoinUrl = result.meetingJoinUrl;

                this.showNotification('会议创建成功', 'success');
                return { success: true, data: result };
            } else {
                throw new Error(result.msg || '创建会议失败');
            }
        } catch (error) {
            this.showNotification(`创建会议失败: ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }

    // 结束会议任务
    async stopMeeting(taskId) {
        try {
            this.showNotification('正在结束会议...', 'info');

            const result = await this.apiCall(`/meetings/${taskId}/stop`, {
                method: 'POST'
            });

            if (result.code === 200) {
                this.showNotification('会议已结束，正在处理录音...', 'success');
                return { success: true };
            } else {
                throw new Error(result.msg || '结束会议失败');
            }
        } catch (error) {
            this.showNotification(`结束会议失败: ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }

    // 获取会议详情
    async getMeetingDetails(taskId) {
        try {
            const result = await this.apiCall(`/meetings/${taskId}`);

            if (result.code === 200) {
                return { success: true, data: result };
            } else {
                throw new Error(result.msg || '获取会议详情失败');
            }
        } catch (error) {
            console.error('获取会议详情失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 获取会议列表
    async getMeetingList(skip = 0, limit = 10) {
        try {
            const result = await this.apiCall(`/meetings?skip=${skip}&limit=${limit}`);

            if (result.code === 200) {
                return { success: true, data: result.data };
            } else {
                throw new Error(result.msg || '获取会议列表失败');
            }
        } catch (error) {
            console.error('获取会议列表失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 任务3.4：会议历史列表功能
    async loadMeetingList(reset = false) {
        if (this.meetingList.loading) return;

        if (reset) {
            this.meetingList.currentPage = 0;
            this.meetingList.data = [];
            this.meetingList.hasMore = true;
        }

        if (!this.meetingList.hasMore) return;

        this.meetingList.loading = true;
        this.showLoadingState();

        try {
            const skip = this.meetingList.currentPage * this.meetingList.pageSize;
            const result = await this.getMeetingList(skip, this.meetingList.pageSize);

            if (result.success) {
                const newMeetings = result.data;

                if (reset) {
                    this.meetingList.data = newMeetings;
                } else {
                    this.meetingList.data = [...this.meetingList.data, ...newMeetings];
                }

                this.meetingList.currentPage++;
                this.meetingList.hasMore = newMeetings.length === this.meetingList.pageSize;
                this.meetingList.totalCount = result.total || this.meetingList.data.length;

                this.renderMeetingList();
                this.hideLoadingState();

                // 如果没有数据，显示空状态
                if (this.meetingList.data.length === 0) {
                    this.showEmptyState();
                }
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载会议列表失败:', error);
            this.showNotification(`加载会议列表失败: ${error.message}`, 'error');
            this.hideLoadingState();

            // 如果是首次加载失败，显示错误状态
            if (this.meetingList.data.length === 0) {
                this.showErrorState();
            }
        } finally {
            this.meetingList.loading = false;
        }
    }

    // 渲染会议列表
    renderMeetingList() {
        const meetingListContainer = document.getElementById('meeting-list');
        const meetingItems = document.getElementById('meeting-items');

        if (!meetingListContainer) return;

        // 显示会议项容器
        if (meetingItems) {
            meetingItems.classList.remove('hidden');
        }

        // 清空现有内容（除了加载状态）
        const existingItems = meetingListContainer.querySelectorAll('.meeting-item');
        existingItems.forEach(item => item.remove());

        // 渲染会议项
        this.meetingList.data.forEach(meeting => {
            const meetingElement = this.createMeetingListItem(meeting);
            meetingListContainer.appendChild(meetingElement);
        });

        // 添加加载更多按钮
        if (this.meetingList.hasMore && this.meetingList.data.length > 0) {
            this.addLoadMoreButton();
        }
    }

    // 创建会议列表项
    createMeetingListItem(meeting) {
        const item = document.createElement('div');
        item.className = 'meeting-item p-4 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-all cursor-pointer';
        item.dataset.meetingId = meeting.taskId;

        // 格式化时间 - 使用正确的字段名
        const startTime = new Date(meeting.createdAt);
        const duration = this.formatDuration(meeting.durationInSeconds || 0);
        const status = this.getMeetingStatusInfo(meeting.taskStatus);

        item.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r ${status.gradient} rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">
                    <i class="fas ${status.icon}"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="text-white font-medium mb-1 truncate">${meeting.title || this.generateMeetingTitle(startTime)}</h3>
                    <div class="flex items-center space-x-3 text-xs text-gray-400 mb-2">
                        <span class="flex items-center">
                            <i class="fas fa-calendar-alt mr-1"></i>
                            ${startTime.toLocaleDateString('zh-CN')} ${startTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock mr-1"></i>
                            ${duration}
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="badge ${status.badgeClass} text-xs">
                            <i class="fas ${status.icon} mr-1"></i>
                            ${status.text}
                        </span>
                        ${meeting.wordCount ? `<span class="text-xs text-gray-500">${meeting.wordCount}字</span>` : ''}
                    </div>
                </div>
            </div>
        `;

        // 点击事件通过事件委托处理，无需在此绑定

        return item;
    }

    // 获取会议状态信息
    getMeetingStatusInfo(status) {
        const statusMap = {
            'COMPLETED': {
                text: '已完成',
                icon: 'fa-check-circle',
                gradient: 'from-green-500 to-green-600',
                badgeClass: 'badge-success'
            },
            'PROCESSING': {
                text: '处理中',
                icon: 'fa-spinner fa-spin',
                gradient: 'from-blue-500 to-blue-600',
                badgeClass: 'badge-processing'
            },
            'FAILED': {
                text: '失败',
                icon: 'fa-exclamation-circle',
                gradient: 'from-red-500 to-red-600',
                badgeClass: 'badge-error'
            },
            'PENDING': {
                text: '等待中',
                icon: 'fa-clock',
                gradient: 'from-yellow-500 to-yellow-600',
                badgeClass: 'badge-warning'
            }
        };

        return statusMap[status] || statusMap['PENDING'];
    }

    // 生成会议标题
    generateMeetingTitle(startTime) {
        return `会议_${startTime.getFullYear()}-${String(startTime.getMonth() + 1).padStart(2, '0')}-${String(startTime.getDate()).padStart(2, '0')} ${String(startTime.getHours()).padStart(2, '0')}:${String(startTime.getMinutes()).padStart(2, '0')}`;
    }

    // 任务3.5：详情页轮询机制和音频播放功能
    async loadMeetingDetail(meetingId) {
        try {
            const result = await this.getMeetingDetails(meetingId);

            if (result.success) {
                const meetingData = result.data;
                this.renderMeetingDetail(meetingData);

                // 如果会议还在处理中，启动轮询
                if (meetingData.taskStatus === 'PROCESSING') {
                    this.startDetailPolling(meetingId);
                } else {
                    this.stopDetailPolling();
                }

                return { success: true, data: meetingData };
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载会议详情失败:', error);
            this.showNotification(`加载会议详情失败: ${error.message}`, 'error');
            this.showDetailErrorState();
            return { success: false, error: error.message };
        }
    }

    // 渲染会议详情
    renderMeetingDetail(meetingData) {
        meetingData = meetingData.data;

        // 更新基本信息
        this.updateMeetingBasicInfo(meetingData);

        // 更新摘要
        this.updateMeetingSummary(meetingData);

        // 更新摘要统计信息
        this.updateSummaryStats(meetingData);

        // 更新转录内容
        this.updateTranscriptContent(meetingData);

        // 更新音频播放器
        this.updateAudioPlayer(meetingData);
    }

    // 更新会议基本信息
    updateMeetingBasicInfo(meetingData) {
        const titleElement = document.getElementById('meeting-title');
        const dateElement = document.getElementById('meeting-date');
        const durationElement = document.getElementById('meeting-duration');
        const participantsElement = document.getElementById('meeting-participants');
        const statusElement = document.getElementById('meeting-status');

        if (titleElement) {
            // 优先使用summary中的title，其次使用生成的标题
            const title = meetingData.summary?.title || this.generateMeetingTitle(new Date(meetingData.createdAt));
            titleElement.textContent = title;
        }

        if (dateElement) {
            const startTime = new Date(meetingData.createdAt);
            dateElement.textContent = `${startTime.toLocaleDateString('zh-CN')} ${startTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
        }

        if (durationElement) {
            durationElement.textContent = this.formatDuration(meetingData.durationInSeconds || 0);
        }

        if (participantsElement) {
            // 根据转录数据计算参会人数
            const participantCount = meetingData.transcripts ?
                new Set(meetingData.transcripts.map(t => t.speakerId)).size : 1;
            participantsElement.textContent = `${participantCount}人参会`;
        }

        if (statusElement) {
            const status = this.getMeetingStatusInfo(meetingData.taskStatus);
            statusElement.className = `badge ${status.badgeClass} text-xs px-3 py-1`;
            statusElement.innerHTML = `<i class="fas ${status.icon} mr-1"></i>${status.text}`;
        }
    }

    // 更新会议摘要
    updateMeetingSummary(meetingData) {
        const summaryContainer = document.getElementById('meeting-summary');
        if (!summaryContainer) return;

        if (meetingData.summary) {
            summaryContainer.innerHTML = this.renderSummaryContent(meetingData.summary);
        } else if (meetingData.taskStatus === 'PROCESSING') {
            summaryContainer.innerHTML = `
                <div class="flex items-center justify-center py-8 text-gray-400">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    正在生成摘要...
                </div>
            `;
        } else {
            summaryContainer.innerHTML = `
                <div class="flex items-center justify-center py-8 text-gray-400">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    暂无摘要内容
                </div>
            `;
        }
    }

    // 渲染摘要内容
    renderSummaryContent(summary) {
        let html = '';

        // 处理段落摘要
        if (summary.paragraph) {
            html += `
                <div class="bg-slate-800/50 rounded-lg p-4 mb-3">
                    <h3 class="font-semibold text-white mb-2">会议摘要</h3>
                    <p class="text-gray-300 leading-relaxed text-sm">${summary.paragraph}</p>
                </div>
            `;
        }

        // 处理对话式摘要
        if (summary.conversational && summary.conversational.length > 0) {
            html += `
                <div class="bg-slate-800/50 rounded-lg p-4 mb-3">
                    <h3 class="font-semibold text-white mb-2">详细摘要</h3>
                    <div class="space-y-3">
                        ${summary.conversational.map(conv => `
                            <div class="border-l-2 border-primary-500/30 pl-3">
                                <div class="flex items-center mb-1">
                                    <span class="text-xs text-primary-400 font-medium">${conv.SpeakerName || '发言人'}</span>
                                </div>
                                <p class="text-gray-300 leading-relaxed text-sm">${conv.Summary}</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        return html || `
            <div class="flex items-center justify-center py-8 text-gray-400">
                <i class="fas fa-exclamation-circle mr-2"></i>
                暂无摘要内容
            </div>
        `;
    }

    // 启动详情页轮询
    startDetailPolling(meetingId) {
        this.stopDetailPolling(); // 先停止现有轮询

        this.detailPolling.interval = setInterval(async () => {
            try {
                const result = await this.getMeetingDetails(meetingId);

                if (result.success) {
                    const meetingData = result.data;
                    this.renderMeetingDetail(meetingData);

                    // 如果状态不再是PROCESSING，停止轮询
                    if (meetingData.taskStatus !== 'PROCESSING') {
                        this.stopDetailPolling();

                        if (meetingData.taskStatus === 'FAILED') {
                            this.showNotification('会议处理失败', 'error');
                        } else if (meetingData.taskStatus === 'COMPLETED') {
                            this.showNotification('会议处理完成', 'success');
                        }
                    }

                    this.detailPolling.retryCount = 0; // 重置重试计数
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('轮询会议详情失败:', error);
                this.detailPolling.retryCount++;

                if (this.detailPolling.retryCount >= this.detailPolling.maxRetries) {
                    this.stopDetailPolling();
                    this.showNotification('更新信息失败', 'error');
                }
            }
        }, this.detailPolling.pollingDelay);
    }

    // 停止详情页轮询
    stopDetailPolling() {
        if (this.detailPolling.interval) {
            clearInterval(this.detailPolling.interval);
            this.detailPolling.interval = null;
            this.detailPolling.retryCount = 0;
        }
    }

    // 更新摘要统计信息
    updateSummaryStats(meetingData) {
        const wordCountElement = document.getElementById('summary-word-count');
        const speechCountElement = document.getElementById('summary-speech-count');

        if (wordCountElement) {
            // 计算转录文本的字数
            const wordCount = meetingData.transcripts ?
                meetingData.transcripts.reduce((total, transcript) => total + (transcript.text?.length || 0), 0) : 0;
            wordCountElement.textContent = wordCount > 0 ? wordCount : '--';
        }

        if (speechCountElement) {
            // 计算发言次数
            const speechCount = meetingData.transcripts ? meetingData.transcripts.length : 0;
            speechCountElement.textContent = speechCount > 0 ? speechCount : '--';
        }
    }

    // 更新转录内容
    updateTranscriptContent(meetingData) {
        const transcriptLoading = document.getElementById('transcript-loading');
        const transcriptContent = document.getElementById('transcript-content');
        const transcriptEmpty = document.getElementById('transcript-empty');

        if (!transcriptContent) return;

        // 隐藏所有状态
        if (transcriptLoading) transcriptLoading.classList.add('hidden');
        if (transcriptEmpty) transcriptEmpty.classList.add('hidden');
        transcriptContent.classList.add('hidden');

        if (meetingData.transcripts && meetingData.transcripts.length > 0) {
            // 有转录内容
            transcriptContent.innerHTML = '';
            meetingData.transcripts.forEach((item, index) => {
                const transcriptItem = this.createTranscriptItem(item, index);
                transcriptContent.appendChild(transcriptItem);
            });
            transcriptContent.classList.remove('hidden');
        } else if (meetingData.taskStatus === 'PROCESSING') {
            // 处理中
            if (transcriptLoading) transcriptLoading.classList.remove('hidden');
        } else {
            // 空状态
            if (transcriptEmpty) transcriptEmpty.classList.remove('hidden');
        }
    }

    // 创建转录项
    createTranscriptItem(transcriptData, index) {
        const item = document.createElement('div');
        item.className = 'transcript-item mb-3';

        const speakerName = `发言人${transcriptData.speakerId || (index + 1)}`;
        const content = transcriptData.text || '';

        // 将毫秒转换为时间格式
        const formatTime = (ms) => {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
        };

        const startTime = transcriptData.startTimeMs ? formatTime(transcriptData.startTimeMs) : '';
        const endTime = transcriptData.endTimeMs ? formatTime(transcriptData.endTimeMs) : '';
        const timeRange = startTime && endTime ? `${startTime} - ${endTime}` : '';

        item.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="speaker-tag">${speakerName}</div>
                <div class="flex-1">
                    <p class="text-gray-200 leading-relaxed mb-1">${content}</p>
                    ${timeRange ? `<span class="text-xs text-gray-400">${timeRange}</span>` : ''}
                </div>
            </div>
        `;
        return item;
    }

    // 更新音频播放器
    updateAudioPlayer(meetingData) {
        const audioElement = document.getElementById('meeting-audio');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const audioCurrentTime = document.getElementById('audio-current-time');
        const audioTotalTime = document.getElementById('audio-total-time');
        const audioProgress = document.getElementById('audio-progress');
        const audioProgressBar = document.getElementById('audio-progress-bar');

        if (!audioElement) return;

        console.log('更新音频播放器，会议ID:', this.pageStates.detail.meetingId); // 调试日志

        // 设置音频源 - 使用正确的字段名
        if (meetingData.audioFileUrl) {
            console.log('设置音频源:', meetingData.audioFileUrl); // 调试日志

            // 确保音频元素完全重置
            audioElement.currentTime = 0;
            audioElement.src = meetingData.audioFileUrl;

            if (playPauseBtn) playPauseBtn.disabled = false;

            // 绑定音频事件
            this.bindAudioEvents(audioElement, playPauseBtn, audioCurrentTime, audioTotalTime, audioProgress, audioProgressBar);
        } else {
            console.log('无音频文件URL'); // 调试日志
            if (playPauseBtn) playPauseBtn.disabled = true;
        }
    }

    // 重置音频播放器
    resetAudioPlayer() {
        const audioElement = document.getElementById('meeting-audio');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const audioCurrentTime = document.getElementById('audio-current-time');
        const audioTotalTime = document.getElementById('audio-total-time');
        const audioProgress = document.getElementById('audio-progress');

        if (audioElement) {
            // 停止播放并清除源
            audioElement.pause();
            audioElement.currentTime = 0;

            // 移除所有事件监听器（如果存在）
            if (this.audioLoadedHandler) {
                audioElement.removeEventListener('loadedmetadata', this.audioLoadedHandler);
                this.audioLoadedHandler = null;
            }
            if (this.audioPlayHandler) {
                audioElement.removeEventListener('play', this.audioPlayHandler);
                this.audioPlayHandler = null;
            }
            if (this.audioPauseHandler) {
                audioElement.removeEventListener('pause', this.audioPauseHandler);
                this.audioPauseHandler = null;
            }
            if (this.audioTimeUpdateHandler) {
                audioElement.removeEventListener('timeupdate', this.audioTimeUpdateHandler);
                this.audioTimeUpdateHandler = null;
            }
            if (this.audioEndedHandler) {
                audioElement.removeEventListener('ended', this.audioEndedHandler);
                this.audioEndedHandler = null;
            }
            if (this.audioErrorHandler) {
                audioElement.removeEventListener('error', this.audioErrorHandler);
                this.audioErrorHandler = null;
            }

            // 清除音频源和加载状态
            audioElement.src = '';
            audioElement.load(); // 强制重新加载空源，清除缓存状态
        }

        // 重置UI状态
        if (playPauseBtn) {
            playPauseBtn.disabled = true;
            const icon = playPauseBtn.querySelector('i');
            if (icon) icon.className = 'fas fa-play text-sm';
        }

        if (audioCurrentTime) audioCurrentTime.textContent = '00:00';
        if (audioTotalTime) audioTotalTime.textContent = '--:--';
        if (audioProgress) audioProgress.style.width = '0%';

        console.log('音频播放器已重置'); // 调试日志
    }

    // 强制重置音频播放器（用于页面切换）
    forceResetAudioPlayer() {
        const audioElement = document.getElementById('meeting-audio');

        if (audioElement) {
            // 强制停止并清除所有状态
            audioElement.pause();
            audioElement.currentTime = 0;
            audioElement.src = '';
            audioElement.load(); // 强制重新加载

            // 移除所有可能的事件监听器
            const events = ['loadedmetadata', 'play', 'pause', 'timeupdate', 'ended', 'error', 'canplay', 'loadstart'];
            events.forEach(event => {
                audioElement.removeEventListener(event, this[`audio${event.charAt(0).toUpperCase() + event.slice(1)}Handler`]);
            });

            // 清除事件处理器引用
            this.audioLoadedHandler = null;
            this.audioPlayHandler = null;
            this.audioPauseHandler = null;
            this.audioTimeUpdateHandler = null;
            this.audioEndedHandler = null;
            this.audioErrorHandler = null;
        }

        // 重置所有UI元素
        const playPauseBtn = document.getElementById('play-pause-btn');
        const audioCurrentTime = document.getElementById('audio-current-time');
        const audioTotalTime = document.getElementById('audio-total-time');
        const audioProgress = document.getElementById('audio-progress');

        if (playPauseBtn) {
            playPauseBtn.disabled = true;
            const icon = playPauseBtn.querySelector('i');
            if (icon) icon.className = 'fas fa-play text-sm';
        }

        if (audioCurrentTime) audioCurrentTime.textContent = '00:00';
        if (audioTotalTime) audioTotalTime.textContent = '--:--';
        if (audioProgress) audioProgress.style.width = '0%';

        console.log('音频播放器已强制重置'); // 调试日志
    }

    // 绑定音频播放器事件
    bindAudioEvents(audioElement, playPauseBtn, currentTimeElement, totalTimeElement, progressElement, progressBarElement) {
        if (!audioElement) return;

        console.log('绑定音频事件'); // 调试日志

        // 保存事件处理器引用以便后续移除
        this.audioLoadedHandler = () => {
            console.log('音频加载完成，时长:', audioElement.duration); // 调试日志
            if (totalTimeElement) {
                totalTimeElement.textContent = this.formatTime(audioElement.duration);
            }
        };

        this.audioPlayHandler = () => {
            if (playPauseBtn) {
                const icon = playPauseBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-pause text-sm';
                }
            }
        };

        this.audioPauseHandler = () => {
            if (playPauseBtn) {
                const icon = playPauseBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-play text-sm';
                }
            }
        };

        this.audioTimeUpdateHandler = () => {
            if (currentTimeElement) {
                currentTimeElement.textContent = this.formatTime(audioElement.currentTime);
            }

            if (progressElement && audioElement.duration) {
                const progress = (audioElement.currentTime / audioElement.duration) * 100;
                progressElement.style.width = `${progress}%`;
            }
        };

        this.audioEndedHandler = () => {
            if (playPauseBtn) {
                const icon = playPauseBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-play text-sm';
                }
            }

            if (progressElement) {
                progressElement.style.width = '0%';
            }

            if (currentTimeElement) {
                currentTimeElement.textContent = '00:00';
            }
        };

        this.audioErrorHandler = () => {
            console.error('音频加载失败');
            if (playPauseBtn) playPauseBtn.disabled = true;
            this.showNotification('音频加载失败', 'error');
        };

        // 播放/暂停按钮事件
        if (playPauseBtn) {
            playPauseBtn.addEventListener('click', () => {
                if (audioElement.paused) {
                    audioElement.play();
                } else {
                    audioElement.pause();
                }
            });
        }

        // 绑定音频事件
        audioElement.addEventListener('loadedmetadata', this.audioLoadedHandler);
        audioElement.addEventListener('play', this.audioPlayHandler);
        audioElement.addEventListener('pause', this.audioPauseHandler);
        audioElement.addEventListener('timeupdate', this.audioTimeUpdateHandler);
        audioElement.addEventListener('ended', this.audioEndedHandler);
        audioElement.addEventListener('error', this.audioErrorHandler);

        // 进度条点击事件
        if (progressBarElement) {
            progressBarElement.addEventListener('click', (e) => {
                const rect = progressBarElement.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const width = rect.width;
                const clickRatio = clickX / width;

                if (audioElement.duration) {
                    audioElement.currentTime = clickRatio * audioElement.duration;
                }
            });
        }
    }

    // 格式化时间（秒转为MM:SS格式）
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '00:00';

        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);

        return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
    }

    // 导航到会议详情页（已移动到路由导航方法部分，避免重复定义）

    // 显示详情页错误状态
    showDetailErrorState() {
        const detailContent = document.getElementById('detail-content');
        if (detailContent) {
            detailContent.innerHTML = `
                <div class="flex flex-col items-center justify-center h-64 text-gray-400">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                    <h3 class="text-lg font-semibold mb-2">加载失败</h3>
                    <p class="text-sm text-center mb-4">无法加载会议详情，请稍后重试</p>
                    <button id="retry-load-detail" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                        重新加载
                    </button>
                </div>
            `;

            // 绑定重试按钮事件
            const retryBtn = document.getElementById('retry-load-detail');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    const meetingId = this.pageStates.detail.meetingId;
                    if (meetingId) {
                        this.loadMeetingDetail(meetingId);
                    }
                });
            }
        }
    }

    // 显示加载状态
    showLoadingState() {
        const loadingElement = document.getElementById('loading-skeleton');
        const emptyElement = document.getElementById('empty-state');
        const errorElement = document.getElementById('error-state');
        const meetingItems = document.getElementById('meeting-items');

        if (loadingElement) loadingElement.classList.remove('hidden');
        if (emptyElement) emptyElement.classList.add('hidden');
        if (errorElement) errorElement.classList.add('hidden');
        if (meetingItems) meetingItems.classList.add('hidden');
    }

    // 隐藏加载状态
    hideLoadingState() {
        const loadingElement = document.getElementById('loading-skeleton');
        if (loadingElement) {
            loadingElement.classList.add('hidden');
        }
    }

    // 显示空状态
    showEmptyState() {
        const emptyElement = document.getElementById('empty-state');
        if (emptyElement) {
            emptyElement.classList.remove('hidden');
        }
    }

    // 显示错误状态
    showErrorState() {
        const errorElement = document.getElementById('error-state');
        if (errorElement) {
            errorElement.classList.remove('hidden');
        }
    }

    // 添加加载更多按钮
    addLoadMoreButton() {
        const meetingListContainer = document.getElementById('meeting-list');
        if (!meetingListContainer) return;

        // 移除现有的加载更多按钮
        const existingBtn = meetingListContainer.querySelector('.load-more-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        const loadMoreBtn = document.createElement('div');
        loadMoreBtn.className = 'load-more-btn flex justify-center py-4';
        loadMoreBtn.innerHTML = `
            <button class="px-6 py-2 bg-slate-700 text-gray-300 rounded-lg hover:bg-slate-600 transition-colors">
                <i class="fas fa-chevron-down mr-2"></i>
                加载更多
            </button>
        `;

        const button = loadMoreBtn.querySelector('button');
        if (button) {
            button.addEventListener('click', () => {
                this.loadMeetingList(false);
            });
        }

        meetingListContainer.appendChild(loadMoreBtn);
    }

    // 处理分享会议
    handleShareMeeting() {
        const meetingId = this.pageStates.detail.meetingId;
        if (!meetingId) return;

        // 生成分享链接
        const shareUrl = `${window.location.origin}${window.location.pathname}#/meeting/${meetingId}`;

        // 尝试使用Web Share API
        if (navigator.share) {
            navigator.share({
                title: '会议记录分享',
                text: '查看这个会议的详细记录',
                url: shareUrl
            }).catch(err => {
                console.log('分享失败:', err);
                this.fallbackShare(shareUrl);
            });
        } else {
            this.fallbackShare(shareUrl);
        }
    }

    // 备用分享方法
    fallbackShare(url) {
        // 复制到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification('链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.showShareDialog(url);
            });
        } else {
            this.showShareDialog(url);
        }
    }

    // 显示分享对话框
    showShareDialog(url) {
        const dialog = document.createElement('div');
        dialog.className = 'fixed inset-0 bg-black/50 flex items-center justify-center z-50';
        dialog.innerHTML = `
            <div class="bg-slate-800 rounded-xl p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-semibold text-white mb-4">分享会议</h3>
                <div class="bg-slate-700 rounded-lg p-3 mb-4">
                    <input type="text" value="${url}" readonly class="w-full bg-transparent text-white text-sm" id="share-url-input">
                </div>
                <div class="flex space-x-3">
                    <button id="copy-url-btn" class="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                        复制链接
                    </button>
                    <button id="close-share-dialog" class="px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // 绑定事件
        const copyBtn = dialog.querySelector('#copy-url-btn');
        const closeBtn = dialog.querySelector('#close-share-dialog');
        const input = dialog.querySelector('#share-url-input');

        copyBtn.addEventListener('click', () => {
            input.select();
            document.execCommand('copy');
            this.showNotification('链接已复制', 'success');
            document.body.removeChild(dialog);
        });

        closeBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
            }
        });
    }

    // 处理下载会议
    handleDownloadMeeting() {
        const meetingId = this.pageStates.detail.meetingId;
        if (!meetingId) return;

        // 显示下载选项
        const options = [
            { type: 'transcript', label: '下载转录文本', icon: 'fa-file-text' },
            { type: 'summary', label: '下载会议摘要', icon: 'fa-file-alt' },
            { type: 'audio', label: '下载音频文件', icon: 'fa-file-audio' },
            { type: 'all', label: '下载完整报告', icon: 'fa-download' }
        ];

        this.showDownloadDialog(options, meetingId);
    }

    // 显示下载对话框
    showDownloadDialog(options, meetingId) {
        const dialog = document.createElement('div');
        dialog.className = 'fixed inset-0 bg-black/50 flex items-center justify-center z-50';
        dialog.innerHTML = `
            <div class="bg-slate-800 rounded-xl p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-semibold text-white mb-4">下载会议资料</h3>
                <div class="space-y-2 mb-4">
                    ${options.map(option => `
                        <button class="download-option w-full p-3 bg-slate-700 hover:bg-slate-600 rounded-lg text-left transition-colors" data-type="${option.type}">
                            <i class="fas ${option.icon} mr-3 text-primary-400"></i>
                            <span class="text-white">${option.label}</span>
                        </button>
                    `).join('')}
                </div>
                <button id="close-download-dialog" class="w-full px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-500 transition-colors">
                    取消
                </button>
            </div>
        `;

        document.body.appendChild(dialog);

        // 绑定事件
        const downloadOptions = dialog.querySelectorAll('.download-option');
        const closeBtn = dialog.querySelector('#close-download-dialog');

        downloadOptions.forEach(option => {
            option.addEventListener('click', () => {
                const type = option.dataset.type;
                this.downloadMeetingFile(meetingId, type);
                document.body.removeChild(dialog);
            });
        });

        closeBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
        });

        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
            }
        });
    }

    // 下载会议文件
    async downloadMeetingFile(meetingId, type) {
        try {
            this.showNotification('正在准备下载...', 'info');

            // 调用后端API获取下载链接
            const response = await fetch(`${this.config.apiBaseUrl}/meetings/${meetingId}/download/${type}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `meeting_${meetingId}_${type}.${this.getFileExtension(type)}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showNotification('下载完成', 'success');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载失败:', error);
            this.showNotification('下载失败，请稍后重试', 'error');
        }
    }

    // 获取文件扩展名
    getFileExtension(type) {
        const extensions = {
            'transcript': 'txt',
            'summary': 'txt',
            'audio': 'mp3',
            'all': 'pdf'
        };
        return extensions[type] || 'txt';
    }

    // 切换转录侧边栏（移动端）
    toggleTranscriptSidebar() {
        const transcriptSidebar = document.querySelector('.w-full.lg\\:w-96');
        if (transcriptSidebar) {
            transcriptSidebar.classList.toggle('hidden');
        }
    }

    // WebSocket连接管理
    connectWebSocket(meetingJoinUrl) {
        return new Promise((resolve, reject) => {
            try {
                this.meetingState.websocket = new WebSocket(meetingJoinUrl);
                this.meetingState.websocket.binaryType = "arraybuffer";

                this.meetingState.websocket.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.showNotification('实时转录连接已建立', 'success');

                    // 发送开始转录命令
                    const startParams = {
                        header: {
                            name: "StartTranscription",
                            namespace: "SpeechTranscriber",
                        },
                        payload: {
                            format: "pcm",
                        },
                    };
                    this.meetingState.websocket.send(JSON.stringify(startParams));
                    resolve();
                };

                this.meetingState.websocket.onmessage = (msg) => {
                    this.handleWebSocketMessage(msg);
                };

                this.meetingState.websocket.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.showNotification('实时转录连接错误', 'error');
                    reject(error);
                };

                this.meetingState.websocket.onclose = (event) => {
                    console.log('WebSocket连接已关闭:', event);
                    if (this.meetingState.isRecording && this.meetingState.connectionRetryCount < this.meetingState.maxRetryCount) {
                        // 自动重连
                        this.retryWebSocketConnection(meetingJoinUrl);
                    }
                };

            } catch (error) {
                console.error('WebSocket连接失败:', error);
                reject(error);
            }
        });
    }

    // WebSocket重连机制
    retryWebSocketConnection(meetingJoinUrl) {
        this.meetingState.connectionRetryCount++;
        const retryDelay = Math.min(1000 * Math.pow(2, this.meetingState.connectionRetryCount), 10000);

        this.showNotification(`连接断开，${retryDelay/1000}秒后重试...`, 'warning');

        setTimeout(() => {
            if (this.meetingState.isRecording) {
                this.connectWebSocket(meetingJoinUrl).catch(() => {
                    if (this.meetingState.connectionRetryCount >= this.meetingState.maxRetryCount) {
                        this.showNotification('连接失败次数过多，请重新开始会议', 'error');
                        this.forceStopMeeting();
                    }
                });
            }
        }, retryDelay);
    }

    // 处理WebSocket消息
    handleWebSocketMessage(msg) {
        if (typeof msg.data === "string") {
            try {
                const dataJson = JSON.parse(msg.data);

                switch (dataJson.header.name) {
                    case "SentenceBegin":
                        console.log("句子", dataJson.payload.index, "开始");
                        break;

                    case "TranscriptionResultChanged":
                        // 句中识别结果变化事件 - 实时更新
                        this.updateTranscriptInProgress(dataJson.payload);
                        break;

                    case "SentenceEnd":
                        // 句子结束事件 - 添加到转录列表
                        this.addTranscriptSegment(dataJson.payload);
                        break;

                    case "ResultTranslated":
                        console.log("翻译结果:", dataJson.payload.translate_result);
                        break;

                    default:
                        console.log("未处理的消息类型:", dataJson.header.name);
                }
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        }
    }

    // 更新进行中的转录内容
    updateTranscriptInProgress(payload) {
        const transcriptContent = document.getElementById('meeting-transcript-content');
        if (!transcriptContent) return;

        // 移除之前的进行中指示器
        const existingIndicator = transcriptContent.querySelector('.transcript-in-progress');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 添加新的进行中内容
        if (payload.result && payload.result.trim()) {
            const segment = document.createElement('div');
            segment.className = 'transcript-segment transcript-in-progress animate-slide-up opacity-70';
            segment.innerHTML = `
                <p class="text-gray-300 leading-relaxed">${payload.result}</p>
                <span class="text-xs text-gray-500 mt-1 block flex items-center">
                    <span class="loading-dots mr-2">转录中</span>
                    <span class="animate-pulse">...</span>
                </span>
            `;
            transcriptContent.insertBefore(segment, transcriptContent.firstChild);
        }
    }

    // 添加完整的转录片段
    addTranscriptSegment(payload) {
        const transcriptContent = document.getElementById('meeting-transcript-content');
        if (!transcriptContent) return;

        // 移除进行中的指示器
        const inProgressIndicator = transcriptContent.querySelector('.transcript-in-progress');
        if (inProgressIndicator) {
            inProgressIndicator.remove();
        }

        // 构建完整的转录文本
        const fullText = payload.result + (payload.stash_result?.text || '');

        if (fullText && fullText.trim()) {
            const segment = document.createElement('div');
            segment.className = 'transcript-segment animate-slide-up';
            segment.innerHTML = `
                <p class="text-gray-200 leading-relaxed">${fullText}</p>
                <span class="text-xs text-gray-400 mt-1 block">刚刚</span>
            `;

            // 更新现有片段的时间戳
            this.updateTranscriptTimestamps();

            // 添加新片段到顶部
            transcriptContent.insertBefore(segment, transcriptContent.firstChild);

            // 更新字数统计
            this.wordCount += fullText.length;
            const wordCountElement = document.getElementById('word-count');
            if (wordCountElement) {
                wordCountElement.textContent = this.wordCount;
            }

            // 保存到缓冲区
            this.meetingState.transcriptBuffer.push({
                text: fullText,
                timestamp: Date.now(),
                index: payload.index
            });
        }
    }

    // 音频录制器类（使用现代AudioWorkletNode）
    async createAudioRecorder(stream) {
        try {
            const context = new (window.AudioContext || window.webkitAudioContext)();

            // 加载AudioWorklet处理器
            await context.audioWorklet.addModule('./js/audio-processor.js');

            // 创建音频节点
            const audioInput = context.createMediaStreamSource(stream);
            const workletNode = new AudioWorkletNode(context, 'audio-recorder-processor');

            // 监听来自AudioWorklet的消息
            workletNode.port.onmessage = (event) => {
                if (event.data.type === 'audioData') {
                    this.sendAudioDataToWebSocket(event.data.data);
                } else if (event.data.type === 'error') {
                    console.error('AudioWorklet错误:', event.data.error);
                }
            };

            const recorderObj = {
                context: context,
                audioInput: audioInput,
                workletNode: workletNode,

                start: function () {
                    // 连接音频节点
                    this.audioInput.connect(this.workletNode);
                    // 注意：不需要连接到destination，因为我们只是处理数据
                },

                stop: function () {
                    // 断开连接
                    this.audioInput.disconnect();
                    this.workletNode.disconnect();
                },

                clear: function () {
                    // 发送清空缓冲区命令
                    this.workletNode.port.postMessage({ command: 'clear' });
                }
            };

            return recorderObj;

        } catch (error) {
            console.error('创建AudioWorklet录制器失败:', error);
            // 如果AudioWorklet不支持，回退到传统方法
            return this.createLegacyAudioRecorder(stream);
        }
    }

    // 发送音频数据到WebSocket
    sendAudioDataToWebSocket(audioBuffer) {
        if (!this.meetingState.websocket || this.meetingState.websocket.readyState !== WebSocket.OPEN) {
            return;
        }

        if (!this.meetingState.isRecording) {
            return;
        }

        try {
            const arr = new Int8Array(audioBuffer);
            if (arr.length > 0) {
                let tmparr = new Int8Array(1024);
                let j = 0;

                for (let i = 0; i < arr.byteLength; i++) {
                    tmparr[j++] = arr[i];
                    if ((i + 1) % 1024 === 0) {
                        this.meetingState.websocket.send(tmparr);
                        if (arr.byteLength - i - 1 >= 1024) {
                            tmparr = new Int8Array(1024);
                        } else {
                            tmparr = new Int8Array(arr.byteLength - i - 1);
                        }
                        j = 0;
                    }
                    if (i + 1 === arr.byteLength && (i + 1) % 1024 !== 0) {
                        this.meetingState.websocket.send(tmparr);
                    }
                }
            }
        } catch (error) {
            console.error('发送音频数据失败:', error);
        }
    }

    // 传统音频录制器（作为回退方案）
    createLegacyAudioRecorder(stream) {
        console.warn('使用传统音频录制方法（createScriptProcessor）');

        const context = new (window.AudioContext || window.webkitAudioContext)();
        const audioInput = context.createMediaStreamSource(stream);
        const recorder = context.createScriptProcessor(4096, 1, 1);

        const audioData = {
            size: 0,
            buffer: [],
            inputSampleRate: 48000,
            inputSampleBits: 16,
            outputSampleRate: 16000,
            outputSampleBits: 16,
            clear: function () {
                this.buffer = [];
                this.size = 0;
            },
            input: function (data) {
                this.buffer.push(new Float32Array(data));
                this.size += data.length;
            },
            compress: function () {
                const data = new Float32Array(this.size);
                let offset = 0;
                for (let i = 0; i < this.buffer.length; i++) {
                    data.set(this.buffer[i], offset);
                    offset += this.buffer[i].length;
                }

                const compression = parseInt(this.inputSampleRate / this.outputSampleRate);
                const length = data.length / compression;
                const result = new Float32Array(length);
                let index = 0, j = 0;
                while (index < length) {
                    result[index] = data[j];
                    j += compression;
                    index++;
                }
                return result;
            },
            encodePCM: function () {
                const sampleBits = Math.min(this.inputSampleBits, this.outputSampleBits);
                const bytes = this.compress();
                const dataLength = bytes.length * (sampleBits / 8);
                const buffer = new ArrayBuffer(dataLength);
                const data = new DataView(buffer);
                let offset = 0;
                for (let i = 0; i < bytes.length; i++, offset += 2) {
                    const s = Math.max(-1, Math.min(1, bytes[i]));
                    data.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
                }
                return new Blob([data]);
            }
        };

        const sendAudioData = () => {
            if (!this.meetingState.websocket || this.meetingState.websocket.readyState !== WebSocket.OPEN) {
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.sendAudioDataToWebSocket(e.target.result);
            };
            reader.readAsArrayBuffer(audioData.encodePCM());
            audioData.clear();
        };

        const recorderObj = {
            start: function () {
                audioInput.connect(recorder);
                recorder.connect(context.destination);
            },
            stop: function () {
                recorder.disconnect();
            },
            clear: function () {
                audioData.clear();
            }
        };

        recorder.onaudioprocess = function (e) {
            if (!this.meetingState.isRecording) return;

            const inputBuffer = e.inputBuffer.getChannelData(0);
            audioData.input(inputBuffer);
            sendAudioData();
        }.bind(this);

        return recorderObj;
    }

    // 开始音频录制
    async startAudioRecording() {
        try {
            // 请求麦克风权限
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 48000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            this.meetingState.audioStream = stream;

            // 创建音频录制器（异步）
            this.meetingState.audioRecorder = await this.createAudioRecorder(stream);
            this.meetingState.isRecording = true;

            // 启动录制
            this.meetingState.audioRecorder.start();

            this.showNotification('音频录制已开始', 'success');
            return { success: true };

        } catch (error) {
            console.error('启动音频录制失败:', error);
            let errorMessage = '启动音频录制失败';

            if (error.name === 'NotAllowedError') {
                errorMessage = '请允许访问麦克风权限';
            } else if (error.name === 'NotFoundError') {
                errorMessage = '未找到可用的麦克风设备';
            } else if (error.message && error.message.includes('AudioWorklet')) {
                errorMessage = '音频处理器加载失败，已使用备用方案';
                console.warn(errorMessage);
            }

            this.showNotification(errorMessage, 'error');
            return { success: false, error: errorMessage };
        }
    }

    // 停止音频录制
    stopAudioRecording() {
        this.meetingState.isRecording = false;

        if (this.meetingState.audioRecorder) {
            this.meetingState.audioRecorder.stop();
            this.meetingState.audioRecorder = null;
        }

        if (this.meetingState.audioStream) {
            this.meetingState.audioStream.getTracks().forEach(track => track.stop());
            this.meetingState.audioStream = null;
        }

        // 发送停止转录命令
        if (this.meetingState.websocket && this.meetingState.websocket.readyState === WebSocket.OPEN) {
            const stopParams = {
                header: {
                    name: "StopTranscription",
                    namespace: "SpeechTranscriber",
                },
                payload: {},
            };
            this.meetingState.websocket.send(JSON.stringify(stopParams));

            // 延迟关闭WebSocket
            setTimeout(() => {
                if (this.meetingState.websocket) {
                    this.meetingState.websocket.close();
                    this.meetingState.websocket = null;
                }
            }, 2000);
        }

        this.showNotification('音频录制已停止', 'info');
    }

    // 更新转录时间戳
    updateTranscriptTimestamps() {
        const transcriptContent = document.getElementById('meeting-transcript-content');
        if (!transcriptContent) return;

        const segments = transcriptContent.querySelectorAll('.transcript-segment:not(.transcript-in-progress)');
        segments.forEach((segment, index) => {
            const timeSpan = segment.querySelector('span');
            if (timeSpan && !timeSpan.textContent.includes('转录中')) {
                const minutesAgo = index + 1;
                if (minutesAgo === 1) {
                    timeSpan.textContent = '刚刚';
                } else if (minutesAgo < 60) {
                    timeSpan.textContent = `${minutesAgo}分钟前`;
                } else {
                    const hoursAgo = Math.floor(minutesAgo / 60);
                    timeSpan.textContent = `${hoursAgo}小时前`;
                }
            }
        });
    }

    // 检查网络状态并处理会议状态
    handleNetworkStatusForMeeting(isOnline) {
        if (!isOnline && this.meetingInProgress) {
            this.showNotification('网络连接断开，会议可能受到影响', 'warning');
        } else if (isOnline && this.meetingInProgress && this.meetingState.websocket?.readyState !== WebSocket.OPEN) {
            // 网络恢复后尝试重连WebSocket
            if (this.meetingState.meetingJoinUrl) {
                this.showNotification('网络已恢复，正在重新连接...', 'info');
                this.connectWebSocket(this.meetingState.meetingJoinUrl).catch(error => {
                    console.error('重连失败:', error);
                });
            }
        }
    }

    // 页面状态恢复方法
    restoreHomePageState(pageState) {
        // 恢复搜索状态
        if (pageState.searchQuery) {
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.value = pageState.searchQuery;
                this.searchQuery = pageState.searchQuery;
            }
        }

        // 恢复筛选状态
        if (pageState.selectedFilter) {
            this.currentFilter = pageState.selectedFilter;
            this.updateFilterButtons();
        }

        // 恢复滚动位置
        if (pageState.scrollPosition) {
            setTimeout(() => {
                window.scrollTo(0, pageState.scrollPosition);
            }, 100);
        }

        // 重新筛选会议列表
        this.filterMeetings();
    }

    restoreDetailPageState(pageState) {
        // 恢复音频播放位置
        if (pageState.audioCurrentTime) {
            setTimeout(() => {
                const audioPlayer = document.querySelector('audio');
                if (audioPlayer) {
                    audioPlayer.currentTime = pageState.audioCurrentTime;
                }
            }, 500);
        }

        // 恢复滚动位置
        if (pageState.scrollPosition) {
            setTimeout(() => {
                window.scrollTo(0, pageState.scrollPosition);
            }, 100);
        }
    }

    restoreMeetingPageState(pageState) {
        // 恢复会议状态
        if (pageState.startTime) {
            this.meetingStartTime = new Date(pageState.startTime);
            this.meetingInProgress = true;
            this.startTimer();
        }

        // 恢复转录内容
        if (pageState.transcripts && pageState.transcripts.length > 0) {
            const transcriptContent = document.getElementById('meeting-transcript-content');
            if (transcriptContent) {
                transcriptContent.innerHTML = '';
                pageState.transcripts.forEach(transcript => {
                    const segment = document.createElement('div');
                    segment.className = 'transcript-segment';
                    segment.innerHTML = transcript;
                    transcriptContent.appendChild(segment);
                });
            }
        }
    }

    // 更新筛选按钮状态
    updateFilterButtons() {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-primary-600', 'text-white');
            btn.classList.add('bg-dark-700/50', 'text-gray-300');
        });

        const activeBtn = document.querySelector(`[data-filter="${this.currentFilter}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active', 'bg-primary-600', 'text-white');
            activeBtn.classList.remove('bg-dark-700/50', 'text-gray-300');
        }
    }

    // 处理页面刷新和直接访问
    handlePageRefresh() {
        const currentRoute = this.router.getCurrentRoute();

        // 如果是会议详情页，检查会议ID是否有效
        if (currentRoute.route === '/meeting/:id') {
            const meetingId = currentRoute.params.id;
            if (meetingId) {
                // 验证会议ID是否存在（这里先用模拟数据验证）
                const meeting = this.meetings.find(m => m.id === meetingId);
                if (!meeting) {
                    // 会议不存在，跳转到首页
                    this.showNotification('会议记录不存在，已跳转到首页', 'warning');
                    this.router.navigate('/', { replace: true });
                    return;
                }
            }
        }

        // 如果是会议进行中页面，检查是否真的有会议在进行
        if (currentRoute.route === '/meeting-active') {
            if (!this.meetingInProgress) {
                // 没有会议在进行，跳转到首页
                this.showNotification('没有会议在进行中，已跳转到首页', 'warning');
                this.router.navigate('/', { replace: true });
                return;
            }
        }
    }

    bindEvents() {
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebarClose = document.getElementById('sidebar-close');
        const sidebarOverlay = document.getElementById('sidebar-overlay');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleSidebar();
            });
        }
        
        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => this.closeSidebar());
        }
        
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => this.closeSidebar());
        }

        // 开始会议按钮
        const startMeetingBtn = document.getElementById('start-meeting-btn');
        if (startMeetingBtn) {
            startMeetingBtn.addEventListener('click', () => this.handleStartMeeting());
        }

        // 结束会议按钮
        const endMeetingBtn = document.getElementById('end-meeting-btn');
        if (endMeetingBtn) {
            endMeetingBtn.addEventListener('click', () => this.handleEndMeeting());
        }

        // 移动端结束会议按钮
        const endMeetingBtnMobile = document.getElementById('end-meeting-btn-mobile');
        if (endMeetingBtnMobile) {
            endMeetingBtnMobile.addEventListener('click', () => this.handleEndMeeting());
        }

        // 静音按钮
        const muteBtnMobile = document.getElementById('mute-btn-mobile');
        if (muteBtnMobile) {
            muteBtnMobile.addEventListener('click', () => this.toggleMute());
        }

        const muteBtn = document.getElementById('mute-btn');
        if (muteBtn) {
            muteBtn.addEventListener('click', () => this.toggleMute());
        }

        // 暂停会议按钮
        const pauseMeetingBtn = document.getElementById('pause-meeting-btn');
        if (pauseMeetingBtn) {
            pauseMeetingBtn.addEventListener('click', () => this.handlePauseMeeting());
        }

        // 返回首页按钮
        const backToHomeBtn = document.getElementById('back-to-home');
        if (backToHomeBtn) {
            backToHomeBtn.addEventListener('click', () => this.navigateToHome());
        }

        // 详情页刷新按钮
        const refreshDetailBtn = document.getElementById('refresh-detail');
        if (refreshDetailBtn) {
            refreshDetailBtn.addEventListener('click', () => {
                const meetingId = this.pageStates.detail.meetingId;
                if (meetingId) {
                    this.loadMeetingDetail(meetingId);
                }
            });
        }

        // 详情页分享按钮
        const shareMeetingBtn = document.getElementById('share-meeting');
        if (shareMeetingBtn) {
            shareMeetingBtn.addEventListener('click', () => this.handleShareMeeting());
        }

        // 详情页下载按钮
        const downloadMeetingBtn = document.getElementById('download-meeting');
        if (downloadMeetingBtn) {
            downloadMeetingBtn.addEventListener('click', () => this.handleDownloadMeeting());
        }

        // 转录侧边栏切换按钮（移动端）
        const toggleTranscriptBtn = document.getElementById('toggle-transcript-sidebar');
        if (toggleTranscriptBtn) {
            toggleTranscriptBtn.addEventListener('click', () => this.toggleTranscriptSidebar());
        }

        // 重试加载按钮
        const retryLoadBtn = document.getElementById('retry-load');
        if (retryLoadBtn) {
            retryLoadBtn.addEventListener('click', () => {
                this.loadMeetingList(true); // 重置并重新加载
            });
        }

        // 音频播放按钮（这个方法会在updateAudioPlayer中动态绑定）
        // 这里不需要绑定，因为音频播放器是动态创建的

        // 网络状态监听
        window.addEventListener('online', () => this.handleOnlineStatus(true));
        window.addEventListener('offline', () => this.handleOnlineStatus(false));

        // 窗口大小变化
        window.addEventListener('resize', () => this.handleResize());

        // 页面离开前保存状态
        window.addEventListener('beforeunload', () => this.savePageState());

        // 页面可见性变化时保存状态
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.savePageState();
            }
        });

        // 初始化触摸手势
        this.initTouchGestures();

        // 点击主内容区域关闭侧边栏
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.addEventListener('click', (e) => {
                if (window.innerWidth < 768 && this.sidebarOpen) {
                    this.closeSidebar();
                }
            });
        }

        // 会议项点击事件（使用事件委托，兼容动态生成的元素）
        document.addEventListener('click', (e) => {
            const meetingItem = e.target.closest('.meeting-item');
            if (meetingItem) {
                // 获取会议ID
                const meetingId = meetingItem.dataset.meetingId;

                if (meetingId) {
                    // 阻止默认行为和冒泡，但只在确认有meetingId时
                    e.preventDefault();
                    e.stopPropagation();

                    // 添加视觉反馈
                    meetingItem.style.backgroundColor = 'rgba(59, 130, 246, 0.2)';
                    setTimeout(() => {
                        meetingItem.style.backgroundColor = '';
                    }, 200);

                    this.navigateToMeetingDetail(meetingId);
                }
            }
        });

        // 移动端触摸事件处理 - 使用更高优先级的事件捕获
        let touchStartTarget = null;

        document.addEventListener('touchstart', (e) => {
            const meetingItem = e.target.closest('.meeting-item');
            if (meetingItem) {
                touchStartTarget = meetingItem;
                meetingItem.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
            }
        }, { capture: true });

        document.addEventListener('touchend', (e) => {
            if (touchStartTarget) {
                const meetingItem = touchStartTarget;
                const meetingId = meetingItem.dataset.meetingId;

                // 重置样式
                meetingItem.style.backgroundColor = '';

                // 确保触摸也能触发导航
                if (meetingId) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.navigateToMeetingDetail(meetingId);
                }

                touchStartTarget = null;
            }
        }, { capture: true });

        // 搜索功能
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }

        // 筛选按钮
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilter(e.target.dataset.filter));
        });

        // 滚动加载更多
        const meetingList = document.getElementById('meeting-list');
        if (meetingList) {
            meetingList.addEventListener('scroll', () => this.handleScroll());
        }

        // 阻止侧边栏内部点击事件冒泡（但不阻止会议项点击）
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.addEventListener('click', (e) => {
                // 只有当点击的不是会议项时才阻止冒泡
                if (!e.target.closest('.meeting-item')) {
                    e.stopPropagation();
                }
            });
        }

        // 返回首页按钮（已在其他地方绑定，避免重复）
    }

    // 侧边栏管理
    toggleSidebar() {
        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('-translate-x-full');
            overlay.classList.remove('hidden');
            this.sidebarOpen = true;
            
            // 添加动画类
            sidebar.classList.add('animate-slide-in');
            setTimeout(() => {
                sidebar.classList.remove('animate-slide-in');
            }, 300);
        }
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            this.sidebarOpen = false;
        }
    }

    initSidebar() {
        // 在桌面端默认显示侧边栏
        if (window.innerWidth >= 768) {
            this.sidebarOpen = true;
        }
    }

    handleResize() {
        if (window.innerWidth >= 768) {
            // 桌面端自动显示侧边栏
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (sidebar) {
                sidebar.classList.remove('-translate-x-full');
            }
            if (overlay) {
                overlay.classList.add('hidden');
            }
            this.sidebarOpen = true;
        } else {
            // 移动端自动隐藏侧边栏
            this.closeSidebar();
        }

        // 响应式布局调整
        this.adjustResponsiveLayout();
    }

    // 响应式布局调整
    adjustResponsiveLayout() {
        const isMobile = window.innerWidth < 768;
        const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;

        // 调整转录区域高度
        const transcriptArea = document.querySelector('.transcript-area');
        if (transcriptArea) {
            if (isMobile) {
                transcriptArea.style.maxHeight = window.innerHeight < 600 ? '8rem' : '12rem';
            } else {
                transcriptArea.style.maxHeight = '20rem';
            }
        }

        // 移动端优化：调整字体大小
        if (isMobile) {
            document.documentElement.style.fontSize = '14px';
        } else {
            document.documentElement.style.fontSize = '16px';
        }
    }

    // 网络状态管理
    checkOnlineStatus() {
        this.handleOnlineStatus(navigator.onLine);
    }

    handleOnlineStatus(isOnline) {
        this.isOnline = isOnline;
        const offlineBanner = document.getElementById('offline-banner');
        const startMeetingBtn = document.getElementById('start-meeting-btn');

        if (offlineBanner) {
            if (isOnline) {
                offlineBanner.classList.add('hidden');
            } else {
                offlineBanner.classList.remove('hidden');
            }
        }

        if (startMeetingBtn) {
            if (isOnline) {
                startMeetingBtn.disabled = false;
                startMeetingBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                startMeetingBtn.disabled = true;
                startMeetingBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // 处理会议状态相关的网络变化
        this.handleNetworkStatusForMeeting(isOnline);
    }

    // 会议相关方法（集成API调用和WebSocket）
    async handleStartMeeting() {
        if (!this.isOnline) {
            this.showNotification('网络连接不可用，无法开始会议', 'error');
            return;
        }

        // 添加按钮点击效果
        const btn = document.getElementById('start-meeting-btn');
        if (btn) {
            btn.disabled = true;
            btn.classList.add('transform', 'scale-95');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>创建中...';
        }

        try {
            // 1. 创建会议任务
            const createResult = await this.createMeeting();
            if (!createResult.success) {
                throw new Error(createResult.error);
            }

            // 2. 开始音频录制
            const audioResult = await this.startAudioRecording();
            if (!audioResult.success) {
                throw new Error(audioResult.error);
            }

            // 3. 建立WebSocket连接
            await this.connectWebSocket(this.meetingState.meetingJoinUrl);

            // 4. 切换到会议进行中界面
            this.startMeeting();

        } catch (error) {
            console.error('开始会议失败:', error);
            this.showNotification(`开始会议失败: ${error.message}`, 'error');

            // 清理状态
            this.cleanupMeetingState();
        } finally {
            // 恢复按钮状态
            if (btn) {
                btn.disabled = false;
                btn.classList.remove('transform', 'scale-95');
                btn.innerHTML = '<i class="fas fa-microphone mr-2 md:mr-3"></i>开始会议';
            }
        }
    }

    startMeeting() {
        this.meetingInProgress = true;
        this.meetingStartTime = new Date();
        this.wordCount = 0;
        this.meetingState.connectionRetryCount = 0;

        // 使用路由导航到会议页面
        this.navigateToMeetingActive();

        // 开始计时器
        this.startTimer();

        // 初始化转录内容区域
        this.initTranscriptContent();

        this.showNotification('会议已开始，正在进行实时转录', 'success');
    }

    async handleEndMeeting() {
        if (!this.meetingInProgress) return;

        // 显示确认对话框
        if (confirm('确定要结束会议吗？会议记录将被保存。')) {
            await this.endMeeting();
        }
    }

    async endMeeting() {
        this.meetingInProgress = false;

        try {
            // 1. 停止音频录制
            this.stopAudioRecording();

            // 2. 调用后端API结束会议
            if (this.meetingState.currentTaskId) {
                const stopResult = await this.stopMeeting(this.meetingState.currentTaskId);
                if (!stopResult.success) {
                    console.error('结束会议API调用失败:', stopResult.error);
                }
            }

            // 3. 停止计时器
            if (this.timerInterval) {
                clearInterval(this.timerInterval);
                this.timerInterval = null;
            }

            // 4. 清理会议状态
            this.cleanupMeetingState();

            // 5. 使用路由导航回首页
            this.navigateToHome();

            this.showNotification('会议已结束，正在生成会议记录', 'success');

        } catch (error) {
            console.error('结束会议失败:', error);
            this.showNotification(`结束会议失败: ${error.message}`, 'error');
        }
    }

    // 强制停止会议（用于错误处理）
    forceStopMeeting() {
        this.meetingInProgress = false;
        this.stopAudioRecording();
        this.cleanupMeetingState();

        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }

        this.navigateToHome();
        this.showNotification('会议已强制结束', 'warning');
    }

    // 清理会议状态
    cleanupMeetingState() {
        this.meetingState.currentTaskId = null;
        this.meetingState.meetingJoinUrl = null;
        this.meetingState.isRecording = false;
        this.meetingState.transcriptBuffer = [];
        this.meetingState.connectionRetryCount = 0;

        if (this.meetingState.websocket) {
            this.meetingState.websocket.close();
            this.meetingState.websocket = null;
        }

        if (this.meetingState.audioRecorder) {
            this.meetingState.audioRecorder.stop();
            this.meetingState.audioRecorder = null;
        }

        if (this.meetingState.audioStream) {
            this.meetingState.audioStream.getTracks().forEach(track => track.stop());
            this.meetingState.audioStream = null;
        }
    }

    handlePauseMeeting() {
        const btn = document.getElementById('pause-meeting-btn');
        const icon = btn.querySelector('i');

        if (btn.textContent.includes('暂停')) {
            // 暂停会议
            btn.innerHTML = '<i class="fas fa-play mr-2 md:mr-3"></i>继续录制';
            btn.classList.remove('from-yellow-600', 'to-yellow-700', 'hover:from-yellow-700', 'hover:to-yellow-800');
            btn.classList.add('from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
            this.showNotification('录制已暂停', 'warning');
        } else {
            // 继续会议
            btn.innerHTML = '<i class="fas fa-pause mr-2 md:mr-3"></i>暂停录制';
            btn.classList.remove('from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
            btn.classList.add('from-yellow-600', 'to-yellow-700', 'hover:from-yellow-700', 'hover:to-yellow-800');
            this.showNotification('录制已继续', 'success');
        }
    }

    handleMute() {
        const btn = document.getElementById('mute-btn');
        const icon = btn.querySelector('i');

        if (icon.classList.contains('fa-microphone')) {
            // 静音
            icon.classList.remove('fa-microphone');
            icon.classList.add('fa-microphone-slash');
            btn.classList.remove('from-gray-600', 'to-gray-700', 'hover:from-gray-700', 'hover:to-gray-800');
            btn.classList.add('from-red-600', 'to-red-700', 'hover:from-red-700', 'hover:to-red-800');
            this.showNotification('麦克风已静音', 'warning');
        } else {
            // 取消静音
            icon.classList.remove('fa-microphone-slash');
            icon.classList.add('fa-microphone');
            btn.classList.remove('from-red-600', 'to-red-700', 'hover:from-red-700', 'hover:to-red-800');
            btn.classList.add('from-gray-600', 'to-gray-700', 'hover:from-gray-700', 'hover:to-gray-800');
            this.showNotification('麦克风已开启', 'success');
        }
    }

    // 搜索功能
    handleSearch(query) {
        this.searchQuery = query.toLowerCase();
        this.filterMeetings();
    }

    // 筛选功能
    handleFilter(filter) {
        this.currentFilter = filter;
        
        // 更新筛选按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-primary-600', 'text-white');
            btn.classList.add('bg-dark-700/50', 'text-gray-300');
        });
        
        const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active', 'bg-primary-600', 'text-white');
            activeBtn.classList.remove('bg-dark-700/50', 'text-gray-300');
        }
        
        this.filterMeetings();
    }

    // 滚动加载更多
    handleScroll() {
        const meetingList = document.getElementById('meeting-list');
        if (meetingList.scrollTop + meetingList.clientHeight >= meetingList.scrollHeight - 10) {
            // TODO: 在阶段三实现实际的分页加载
            console.log('触发加载更多');
        }
    }

    // 筛选会议列表
    filterMeetings() {
        let filtered = this.meetings;

        // 按状态筛选
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(meeting => meeting.status === this.currentFilter);
        }

        // 按搜索关键词筛选
        if (this.searchQuery) {
            filtered = filtered.filter(meeting => 
                meeting.title.toLowerCase().includes(this.searchQuery)
            );
        }

        this.filteredMeetings = filtered;
        this.renderMeetingList(this.filteredMeetings);
    }







    // 通知系统
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm animate-slide-down`;
        
        const typeConfig = {
            'success': 'bg-green-600 text-white',
            'error': 'bg-red-600 text-white',
            'warning': 'bg-yellow-600 text-white',
            'info': 'bg-blue-600 text-white'
        };

        notification.className += ` ${typeConfig[type] || typeConfig['info']}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // 自动移除通知
        setTimeout(() => {
            notification.classList.add('animate-slide-up');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 计时器功能
    startTimer() {
        this.timerInterval = setInterval(() => {
            if (this.meetingStartTime) {
                const now = new Date();
                const elapsed = Math.floor((now - this.meetingStartTime) / 1000);
                const timerDisplay = document.getElementById('meeting-timer');
                if (timerDisplay) {
                    timerDisplay.textContent = this.formatDuration(elapsed);
                }
            }
        }, 1000);
    }

    // 初始化转录内容区域
    initTranscriptContent() {
        const transcriptContent = document.getElementById('meeting-transcript-content');
        if (transcriptContent) {
            transcriptContent.innerHTML = `
                <div class="transcript-segment animate-slide-up opacity-70">
                    <p class="text-gray-400 italic text-center flex items-center justify-center">
                        <i class="fas fa-microphone mr-2"></i>
                        等待语音输入...
                    </p>
                </div>
            `;
        }
    }

    resetTranscriptContent() {
        this.initTranscriptContent();

        // 重置计时器显示
        const timerDisplay = document.getElementById('meeting-timer');
        if (timerDisplay) {
            timerDisplay.textContent = '00:00:00';
        }

        // 重置字数统计
        const wordCountElement = document.getElementById('word-count');
        if (wordCountElement) {
            wordCountElement.textContent = '0';
        }
        this.wordCount = 0;
    }

    // 路由导航方法
    navigateToHome() {
        this.savePageState();
        this.router.navigate('/');
    }

    navigateToMeetingDetail(meetingId) {
        // 简化的页面切换逻辑
        const homeContent = document.getElementById('home-content');
        const detailContent = document.getElementById('detail-content');

        if (homeContent && detailContent) {
            homeContent.classList.add('hidden');
            detailContent.classList.remove('hidden');

            // 更新页面标题
            const meetingTitle = document.getElementById('meeting-title');
            if (meetingTitle) {
                meetingTitle.textContent = `会议详情 - ${meetingId}`;
            }
        }

        // 如果路由系统存在，也调用它
        if (this.router && typeof this.router.navigate === 'function') {
            this.savePageState();
            this.router.navigate(`/meeting/${meetingId}`);
        }
    }

    // 导航到首页
    navigateToHome() {
        const homeContent = document.getElementById('home-content');
        const detailContent = document.getElementById('detail-content');
        const meetingContent = document.getElementById('meeting-content');

        // 隐藏所有其他页面
        if (detailContent) detailContent.classList.add('hidden');
        if (meetingContent) meetingContent.classList.add('hidden');

        // 显示首页
        if (homeContent) homeContent.classList.remove('hidden');

        // 如果路由系统存在，也调用它
        if (this.router && typeof this.router.navigate === 'function') {
            this.router.navigate('/');
        }
    }

    navigateToMeetingActive() {
        this.savePageState();
        this.router.navigate('/meeting-active');
    }

    // 页面显示方法（由路由调用）
    showHomePage(state = {}) {
        const homeContent = document.getElementById('home-content');
        const meetingContent = document.getElementById('meeting-content');
        const detailContent = document.getElementById('detail-content');

        if (homeContent) {
            if (meetingContent) meetingContent.classList.add('hidden');
            if (detailContent) detailContent.classList.add('hidden');
            homeContent.classList.remove('hidden');
        }

        // 关闭侧边栏（移动端）
        if (window.innerWidth < 768) {
            this.closeSidebar();
        }

        this.currentPage = 'home';

        // 停止详情页轮询
        this.stopDetailPolling();

        // 重置音频播放器
        this.forceResetAudioPlayer();

        // 恢复页面状态
        if (state.pageState) {
            this.restoreHomePageState(state.pageState);
        }
    }

    showMeetingDetail(meetingId, state = {}) {
        // 切换到详情页
        const homeContent = document.getElementById('home-content');
        const meetingContent = document.getElementById('meeting-content');
        const detailContent = document.getElementById('detail-content');

        if (homeContent && detailContent) {
            homeContent.classList.add('hidden');
            if (meetingContent) meetingContent.classList.add('hidden');
            detailContent.classList.remove('hidden');
        }

        // 关闭侧边栏（移动端）
        if (window.innerWidth < 768) {
            this.closeSidebar();
        }

        this.currentPage = 'detail';

        // 保存当前查看的会议ID
        this.pageStates.detail.meetingId = meetingId;

        // 恢复页面状态
        if (state.pageState) {
            this.restoreDetailPageState(state.pageState);
        }
    }

    showMeetingActive(state = {}) {
        // 切换到会议进行中页面
        const homeContent = document.getElementById('home-content');
        const meetingContent = document.getElementById('meeting-content');
        const detailContent = document.getElementById('detail-content');

        if (homeContent && meetingContent) {
            homeContent.classList.add('hidden');
            if (detailContent) detailContent.classList.add('hidden');
            meetingContent.classList.remove('hidden');
        }

        // 关闭侧边栏（移动端）
        if (window.innerWidth < 768) {
            this.closeSidebar();
        }

        this.currentPage = 'meeting-active';

        // 停止详情页轮询
        this.stopDetailPolling();
    }

    // 切换静音状态
    toggleMute() {
        this.isMuted = !this.isMuted;

        const muteBtns = [
            document.getElementById('mute-btn'),
            document.getElementById('mute-btn-mobile')
        ];

        muteBtns.forEach(btn => {
            if (btn) {
                const icon = btn.querySelector('i');
                if (this.isMuted) {
                    icon.className = 'fas fa-microphone-slash text-lg';
                    btn.classList.remove('from-gray-600', 'to-gray-700');
                    btn.classList.add('from-red-600', 'to-red-700');
                } else {
                    icon.className = 'fas fa-microphone text-lg';
                    btn.classList.remove('from-red-600', 'to-red-700');
                    btn.classList.add('from-gray-600', 'to-gray-700');
                }
            }
        });

        this.showNotification(
            this.isMuted ? '麦克风已静音' : '麦克风已开启',
            this.isMuted ? 'warning' : 'success'
        );

        this.showNotification('会议详情加载完成', 'success');
    }

    showMeetingPage(state = {}) {
        // 切换到会议进行中页面
        const homeContent = document.getElementById('home-content');
        const meetingContent = document.getElementById('meeting-content');
        const detailContent = document.getElementById('detail-content');

        if (homeContent && meetingContent) {
            homeContent.classList.add('hidden');
            if (detailContent) detailContent.classList.add('hidden');
            meetingContent.classList.remove('hidden');
        }

        // 关闭侧边栏（移动端）
        if (window.innerWidth < 768) {
            this.closeSidebar();
        }

        this.currentPage = 'meeting';

        // 恢复会议状态
        if (state.pageState) {
            this.restoreMeetingPageState(state.pageState);
        }
    }

    // 显示会议详情页
    showMeetingDetailPage(meetingId, state = {}) {
        // 切换到会议详情页
        const homeContent = document.getElementById('home-content');
        const meetingContent = document.getElementById('meeting-content');
        const detailContent = document.getElementById('detail-content');

        if (homeContent && detailContent) {
            homeContent.classList.add('hidden');
            if (meetingContent) meetingContent.classList.add('hidden');
            detailContent.classList.remove('hidden');
        }

        // 关闭侧边栏（移动端）
        if (window.innerWidth < 768) {
            this.closeSidebar();
        }

        this.currentPage = 'detail';

        // 保存会议ID到页面状态
        this.pageStates.detail.meetingId = meetingId;

        // 停止之前的轮询
        this.stopDetailPolling();

        // 立即强制重置音频播放器
        this.forceResetAudioPlayer();

        // 加载会议详情
        this.loadMeetingDetail(meetingId);

        // 恢复页面状态
        if (state.pageState) {
            this.restoreDetailPageState(state.pageState);
        }
    }

    // 恢复详情页状态
    restoreDetailPageState(pageState) {
        if (pageState.scrollPosition) {
            setTimeout(() => {
                window.scrollTo(0, pageState.scrollPosition);
            }, 100);
        }

        if (pageState.audioCurrentTime && pageState.audioCurrentTime > 0) {
            setTimeout(() => {
                const audioElement = document.getElementById('meeting-audio');
                if (audioElement) {
                    audioElement.currentTime = pageState.audioCurrentTime;
                }
            }, 500);
        }
    }

    // 音频播放控制
    handleAudioPlayPause() {
        const btn = document.getElementById('play-pause-btn');
        const icon = btn.querySelector('i');

        if (icon.classList.contains('fa-play')) {
            // 开始播放
            icon.classList.remove('fa-play');
            icon.classList.add('fa-pause');
            this.showNotification('开始播放录音', 'info');
        } else {
            // 暂停播放
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
            this.showNotification('暂停播放录音', 'info');
        }
    }

    // 触摸手势支持
    initTouchGestures() {
        let startX = 0;
        let startY = 0;
        let startTime = 0;

        // 侧边栏滑动手势
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('main');

        if (mainContent) {
            mainContent.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                startTime = Date.now();
            }, { passive: true });

            // 合并touchend事件处理：滑动手势 + 双击返回顶部
            let lastTap = 0;
            mainContent.addEventListener('touchend', (e) => {
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const endTime = Date.now();

                const deltaX = endX - startX;
                const deltaY = endY - startY;
                const deltaTime = endTime - startTime;

                // 检测水平滑动手势
                if (Math.abs(deltaX) > Math.abs(deltaY) &&
                    Math.abs(deltaX) > 50 &&
                    deltaTime < 300) {

                    if (deltaX > 0 && startX < 50 && !this.sidebarOpen) {
                        // 从左边缘向右滑动，打开侧边栏
                        this.openSidebar();
                    } else if (deltaX < 0 && this.sidebarOpen) {
                        // 向左滑动，关闭侧边栏
                        this.closeSidebar();
                    }
                } else {
                    // 检测双击返回顶部（仅在非滑动时）
                    const currentTime = new Date().getTime();
                    const tapLength = currentTime - lastTap;

                    if (tapLength < 500 && tapLength > 0) {
                        // 双击检测
                        window.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                        });
                        e.preventDefault();
                    }
                    lastTap = currentTime;
                }
            }, { passive: true });
        }

        // 长按菜单（会议项）- 已移除，使用上面的触摸事件处理
    }

    // 显示会议上下文菜单
    showMeetingContextMenu(meetingItem) {
        // 简单的上下文菜单实现
        const options = ['查看详情', '分享会议', '删除会议'];
        const choice = confirm('会议操作菜单\n1. 查看详情\n2. 分享会议\n3. 删除会议\n\n点击确定查看详情，取消关闭菜单');

        if (choice) {
            const meetingId = meetingItem.dataset.meetingId;
            this.navigateToMeetingDetail(meetingId);
        }
    }

    // 路由辅助方法
    getCurrentPageInfo() {
        const currentRoute = this.router.getCurrentRoute();
        return {
            page: this.currentPage,
            route: currentRoute.route,
            params: currentRoute.params,
            state: currentRoute.state
        };
    }

    // 获取当前会议ID（如果在详情页）
    getCurrentMeetingId() {
        const currentRoute = this.router.getCurrentRoute();
        if (currentRoute.route === '/meeting/:id') {
            return currentRoute.params.id;
        }
        return null;
    }

    // 检查是否可以使用浏览器后退
    canGoBack() {
        return window.history.length > 1;
    }

    // 安全的后退导航
    goBack() {
        if (this.canGoBack()) {
            window.history.back();
        } else {
            this.navigateToHome();
        }
    }

    // 工具方法
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.aiMeetingApp = new AIMeetingApp();
});

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIMeetingApp;
}
