--
-- 主表: 实时会议任务表 (meeting_tasks)
-- 作用: 存储每个会议任务的核心元数据、状态和结果URL。
--
CREATE TABLE `meeting_tasks` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `task_id` VARCHAR(64) NOT NULL COMMENT '来自上游API的唯一任务ID',
  `task_key` VARCHAR(128) NULL COMMENT '客户端自定义的任务标识',
  `task_status` ENUM('NEW', 'PROCESSING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'NEW' COMMENT '任务状态',
  `meeting_join_url` TEXT NULL COMMENT 'WebSocket连接地址',
  `output_mp3_path` TEXT NULL COMMENT '会议录音MP3下载地址',
  `meeting_assistance_url` TEXT DEFAULT NULL COMMENT '智能会议纪要地址',
  `transcription_url` TEXT DEFAULT NULL COMMENT '语音转录结果地址',
  `summarization_url` TEXT DEFAULT NULL COMMENT '会议摘要信息地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_task_key` (`task_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时会议任务主表';


--
-- 关联表1: 语音转录结果表 (transcriptions)
-- 作用: 存储详细的语音转文字结果，包含段落、词语、时间戳和说话人信息。
--
CREATE TABLE `transcriptions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `task_id` VARCHAR(64) NOT NULL COMMENT '关联到meeting_tasks的任务ID',
  `audio_duration_ms` INT UNSIGNED NULL COMMENT '音频总时长（毫秒）',
  `paragraphs` JSON NULL COMMENT '段落与词语信息数组，存储完整的JSON结构',
  `audio_segments` JSON NULL COMMENT '音频有效片段的起止时间数组',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  CONSTRAINT `fk_transcriptions_task_id` FOREIGN KEY (`task_id`) REFERENCES `meeting_tasks` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语音转录结果表';


--
-- 关联表2: 会议摘要信息表 (summarizations)
-- 作用: 存储AI生成的全文摘要和分角色摘要。
--
CREATE TABLE `summarizations` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `task_id` VARCHAR(64) NOT NULL COMMENT '关联到meeting_tasks的任务ID',
  `paragraph_title` VARCHAR(255) NULL COMMENT 'AI生成的全文摘要标题',
  `paragraph_summary` TEXT NULL COMMENT 'AI生成的全文摘要内容',
  `conversational_summary` JSON NULL COMMENT '分角色摘要数组，存储完整的JSON结构',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  CONSTRAINT `fk_summarizations_task_id` FOREIGN KEY (`task_id`) REFERENCES `meeting_tasks` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议摘要信息表';


--
-- 关联表3: 智能会议纪要表 (meeting_assistances)
-- 作用: 存储智能分析得出的关键词和会议分类。
--
CREATE TABLE `meeting_assistances` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主-键',
  `task_id` VARCHAR(64) NOT NULL COMMENT '关联到meeting_tasks的任务ID',
  `keywords` JSON NULL COMMENT '关键词列表',
  `classifications` JSON NULL COMMENT '会议分类及置信度得分',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  CONSTRAINT `fk_assistances_task_id` FOREIGN KEY (`task_id`) REFERENCES `meeting_tasks` (`task_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能会议纪要表';