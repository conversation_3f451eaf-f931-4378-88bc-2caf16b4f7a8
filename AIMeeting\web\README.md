# AI会议记录 FastAPI 后端服务

基于阿里云听悟API的实时会议转录和分析服务。

## 功能特性

- 🎤 实时会议转录
- 📝 AI智能摘要
- 🔍 关键词提取
- 📊 会议分析
- 🗄️ 会议记录管理
- ⚡ 异步处理和后台轮询

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并填入配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# MySQL数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/ai_meeting

# 阿里云API配置
ALIYUN_API_BASE_URL=tingwu.cn-beijing.aliyuncs.com
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_APP_KEY=your_app_key

# 本地服务配置
SVC_AUTH=your_service_auth_token
POLLING_INTERVAL_SECONDS=30
```

### 3. 初始化数据库

```bash
python init_db.py
```

### 4. 启动服务

```bash
python run.py
```

服务将在 `http://localhost:8000` 启动。

## API 文档

启动服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API 接口

### 认证

所有API请求需要在Header中包含认证信息：

```
Authorization: your_service_auth_token
```

### 主要接口

#### 1. 创建会议

```http
POST /api/v1/meetings
```

响应：
```json
{
  "code": 200,
  "taskId": "task_123456",
  "meetingJoinUrl": "https://..."
}
```

#### 2. 结束会议

```http
POST /api/v1/meetings/{task_id}/stop
```

#### 3. 获取会议列表

```http
GET /api/v1/meetings?skip=0&limit=20
```

#### 4. 获取会议详情

```http
GET /api/v1/meetings/{task_id}
```

## 项目结构

```
web/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用主文件
│   ├── models.py            # 数据库模型
│   ├── schemas.py           # Pydantic模型
│   ├── crud.py              # 数据库操作
│   ├── database.py          # 数据库连接
│   ├── config.py            # 配置管理
│   └── aliyun_meeting.py    # 阿里云API封装
├── requirements.txt         # 依赖包
├── run.py                  # 启动脚本
├── init_db.py              # 数据库初始化
├── .env.example            # 环境变量模板
└── README.md               # 说明文档
```

## 开发说明

### 数据库模型

- `MeetingTask`: 会议任务主表
- `TranscriptionResult`: 转录结果
- `SummarizationResult`: 摘要结果
- `AssistanceResult`: 会议助手结果

### 后台任务

系统会自动启动后台轮询任务，定期检查阿里云API的任务状态并获取结果。

### 错误处理

所有API都使用统一的错误响应格式：

```json
{
  "code": 500,
  "msg": "错误信息"
}
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t ai-meeting-api .

# 运行容器
docker run -d -p 8000:8000 --env-file .env ai-meeting-api
```

### 生产环境

1. 使用 Gunicorn 或 uWSGI 作为 WSGI 服务器
2. 配置 Nginx 作为反向代理
3. 使用 MySQL 或 PostgreSQL 作为生产数据库
4. 配置日志轮转和监控

## 许可证

MIT License
