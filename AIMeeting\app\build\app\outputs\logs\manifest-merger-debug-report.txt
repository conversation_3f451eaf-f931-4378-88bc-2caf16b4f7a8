-- Merging decision tree log ---
application
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:51:5-82:19
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] G:\Temp\.gradle\caches\transforms-4\2c8e3f26cdaaa95189a94d96e61fd917\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] G:\Temp\.gradle\caches\transforms-4\2c8e3f26cdaaa95189a94d96e61fd917\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:1:1-96:12
MERGED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:1:1-96:12
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:permission_handler_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_native_splash] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] G:\Temp\.gradle\caches\transforms-4\c696e2fadb5ec69b0aff473dfe51b413\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] G:\Temp\.gradle\caches\transforms-4\103df599988d4da89a096268f4456e78\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\a59e795fd55938903f94c03a49858d93\transformed\jetified-window-java-1.0.0-beta04\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.fragment:fragment:1.3.6] G:\Temp\.gradle\caches\transforms-4\36d747a72503ffd257310090b22f27c9\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] G:\Temp\.gradle\caches\transforms-4\c7fec9257ab2fd8a5a58111dab4297ef\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] G:\Temp\.gradle\caches\transforms-4\9932f84dfaac7952720b2f641b9070dc\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] G:\Temp\.gradle\caches\transforms-4\b9532f25031491aead0e597658f13283\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] G:\Temp\.gradle\caches\transforms-4\1fc726f5d37e00bc0df1e686df43e3e8\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] G:\Temp\.gradle\caches\transforms-4\df0f15d909ec17f452b3e7ac09fb7e99\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.8.0] G:\Temp\.gradle\caches\transforms-4\fbe9938d3bef236a2a7835c8caf80626\transformed\webkit-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] G:\Temp\.gradle\caches\transforms-4\7d99e21d036517c2a918542a740b3cf7\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] G:\Temp\.gradle\caches\transforms-4\f387cc8e4289f86d8226f2d081ac8edd\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] G:\Temp\.gradle\caches\transforms-4\8f1144a11219e78844c93e397b559c95\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] G:\Temp\.gradle\caches\transforms-4\bae69568d33997b4aecfa7dc826900b9\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] G:\Temp\.gradle\caches\transforms-4\980ed5b1b1e5c721b747c3541618e78b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core-ktx:1.9.0] G:\Temp\.gradle\caches\transforms-4\1a5c489a55982c94f714665d8d26d466\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] G:\Temp\.gradle\caches\transforms-4\42b1f012e0b23432ba76053455d62433\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] G:\Temp\.gradle\caches\transforms-4\1be292016ca4517b55d41ec221081669\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] G:\Temp\.gradle\caches\transforms-4\c07ab2ead90e6c3daf736e331288a8cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] G:\Temp\.gradle\caches\transforms-4\1e63cbbd63904e074b06c1402ee0eb1c\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] G:\Temp\.gradle\caches\transforms-4\2c8e3f26cdaaa95189a94d96e61fd917\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] G:\Temp\.gradle\caches\transforms-4\60419e6a3c5b5e24b5dd81a5d9eb6ddb\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] G:\Temp\.gradle\caches\transforms-4\ba2085e7fc50443b787f62c855687a45\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] G:\Temp\.gradle\caches\transforms-4\71516ee3de902d6608bf959f5974065a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] G:\Temp\.gradle\caches\transforms-4\a3f96ac62e0a9f1a59fabe9344887dee\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] G:\Temp\.gradle\caches\transforms-4\2c9f74a9568015c0c5b5fbbbe48da751\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] G:\Temp\.gradle\caches\transforms-4\087cdab7c8e620c4c70e4151fb03c9e0\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] G:\Temp\.gradle\caches\transforms-4\bb8cf323bb1fabdea918d641733791d9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:3:5-4:53
MERGED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:3:5-4:53
MERGED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:3:5-4:53
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:4:9-51
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:5:5-6:65
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:6:9-63
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:7:5-8:65
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:8:9-63
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:9:5-10:65
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:10:9-63
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:11:5-12:62
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:12:9-60
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:13:5-14:62
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:14:9-60
uses-permission#android.permission.NFC
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:15:5-16:48
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:16:9-46
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:18:5-19:66
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:19:9-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:20:5-21:67
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:21:9-65
uses-permission#android.permission.CAMERA
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:23:5-24:51
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:24:9-49
uses-permission#android.permission.RECORD_AUDIO
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:25:5-26:57
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:26:9-55
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:27:5-28:66
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:28:9-64
uses-permission#android.permission.MICROPHONE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:29:5-30:55
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:30:9-53
uses-permission#android.permission.AUDIO_CAPTURE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:31:5-32:58
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:32:9-56
uses-permission#android.permission.VIDEO_CAPTURE
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:33:5-34:58
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:34:9-56
uses-permission#android.permission.CAPTURE_SECURE_VIDEO_OUTPUT
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:35:5-36:72
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:36:9-70
uses-permission#android.permission.CAPTURE_VIDEO_OUTPUT
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:37:5-38:65
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:38:9-63
uses-permission#android.permission.CAPTURE_AUDIO_OUTPUT
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:39:5-40:65
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:40:9-63
uses-feature#android.hardware.camera
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:42:5-43:49
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:43:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:44:5-45:59
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:45:9-57
uses-feature#android.hardware.microphone
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:46:5-47:53
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:47:9-51
uses-permission#android.permission.WAKE_LOCK
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:49:5-50:54
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:50:9-52
queries
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:88:5-95:15
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:89:9-94:18
action#android.intent.action.PROCESS_TEXT
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:90:13-91:68
	android:name
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:91:17-66
data
ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:92:13-93:48
	android:mimeType
		ADDED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:93:17-46
uses-sdk
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] G:\Temp\.gradle\caches\transforms-4\c696e2fadb5ec69b0aff473dfe51b413\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] G:\Temp\.gradle\caches\transforms-4\c696e2fadb5ec69b0aff473dfe51b413\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] G:\Temp\.gradle\caches\transforms-4\103df599988d4da89a096268f4456e78\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] G:\Temp\.gradle\caches\transforms-4\103df599988d4da89a096268f4456e78\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\a59e795fd55938903f94c03a49858d93\transformed\jetified-window-java-1.0.0-beta04\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window-java:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\a59e795fd55938903f94c03a49858d93\transformed\jetified-window-java-1.0.0-beta04\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.fragment:fragment:1.3.6] G:\Temp\.gradle\caches\transforms-4\36d747a72503ffd257310090b22f27c9\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] G:\Temp\.gradle\caches\transforms-4\36d747a72503ffd257310090b22f27c9\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] G:\Temp\.gradle\caches\transforms-4\c7fec9257ab2fd8a5a58111dab4297ef\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] G:\Temp\.gradle\caches\transforms-4\c7fec9257ab2fd8a5a58111dab4297ef\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] G:\Temp\.gradle\caches\transforms-4\9932f84dfaac7952720b2f641b9070dc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] G:\Temp\.gradle\caches\transforms-4\9932f84dfaac7952720b2f641b9070dc\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] G:\Temp\.gradle\caches\transforms-4\b9532f25031491aead0e597658f13283\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] G:\Temp\.gradle\caches\transforms-4\b9532f25031491aead0e597658f13283\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] G:\Temp\.gradle\caches\transforms-4\1fc726f5d37e00bc0df1e686df43e3e8\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] G:\Temp\.gradle\caches\transforms-4\1fc726f5d37e00bc0df1e686df43e3e8\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] G:\Temp\.gradle\caches\transforms-4\df0f15d909ec17f452b3e7ac09fb7e99\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] G:\Temp\.gradle\caches\transforms-4\df0f15d909ec17f452b3e7ac09fb7e99\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.8.0] G:\Temp\.gradle\caches\transforms-4\fbe9938d3bef236a2a7835c8caf80626\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.8.0] G:\Temp\.gradle\caches\transforms-4\fbe9938d3bef236a2a7835c8caf80626\transformed\webkit-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] G:\Temp\.gradle\caches\transforms-4\7d99e21d036517c2a918542a740b3cf7\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] G:\Temp\.gradle\caches\transforms-4\7d99e21d036517c2a918542a740b3cf7\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] G:\Temp\.gradle\caches\transforms-4\f387cc8e4289f86d8226f2d081ac8edd\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] G:\Temp\.gradle\caches\transforms-4\f387cc8e4289f86d8226f2d081ac8edd\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] G:\Temp\.gradle\caches\transforms-4\8f1144a11219e78844c93e397b559c95\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] G:\Temp\.gradle\caches\transforms-4\8f1144a11219e78844c93e397b559c95\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] G:\Temp\.gradle\caches\transforms-4\bae69568d33997b4aecfa7dc826900b9\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] G:\Temp\.gradle\caches\transforms-4\bae69568d33997b4aecfa7dc826900b9\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] G:\Temp\.gradle\caches\transforms-4\980ed5b1b1e5c721b747c3541618e78b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] G:\Temp\.gradle\caches\transforms-4\980ed5b1b1e5c721b747c3541618e78b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] G:\Temp\.gradle\caches\transforms-4\1a5c489a55982c94f714665d8d26d466\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] G:\Temp\.gradle\caches\transforms-4\1a5c489a55982c94f714665d8d26d466\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] G:\Temp\.gradle\caches\transforms-4\42b1f012e0b23432ba76053455d62433\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] G:\Temp\.gradle\caches\transforms-4\42b1f012e0b23432ba76053455d62433\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] G:\Temp\.gradle\caches\transforms-4\1be292016ca4517b55d41ec221081669\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] G:\Temp\.gradle\caches\transforms-4\1be292016ca4517b55d41ec221081669\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] G:\Temp\.gradle\caches\transforms-4\c07ab2ead90e6c3daf736e331288a8cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] G:\Temp\.gradle\caches\transforms-4\c07ab2ead90e6c3daf736e331288a8cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] G:\Temp\.gradle\caches\transforms-4\1e63cbbd63904e074b06c1402ee0eb1c\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] G:\Temp\.gradle\caches\transforms-4\1e63cbbd63904e074b06c1402ee0eb1c\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] G:\Temp\.gradle\caches\transforms-4\2c8e3f26cdaaa95189a94d96e61fd917\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] G:\Temp\.gradle\caches\transforms-4\2c8e3f26cdaaa95189a94d96e61fd917\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] G:\Temp\.gradle\caches\transforms-4\60419e6a3c5b5e24b5dd81a5d9eb6ddb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] G:\Temp\.gradle\caches\transforms-4\60419e6a3c5b5e24b5dd81a5d9eb6ddb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] G:\Temp\.gradle\caches\transforms-4\ba2085e7fc50443b787f62c855687a45\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] G:\Temp\.gradle\caches\transforms-4\ba2085e7fc50443b787f62c855687a45\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] G:\Temp\.gradle\caches\transforms-4\71516ee3de902d6608bf959f5974065a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] G:\Temp\.gradle\caches\transforms-4\71516ee3de902d6608bf959f5974065a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] G:\Temp\.gradle\caches\transforms-4\a3f96ac62e0a9f1a59fabe9344887dee\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] G:\Temp\.gradle\caches\transforms-4\a3f96ac62e0a9f1a59fabe9344887dee\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] G:\Temp\.gradle\caches\transforms-4\2c9f74a9568015c0c5b5fbbbe48da751\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] G:\Temp\.gradle\caches\transforms-4\2c9f74a9568015c0c5b5fbbbe48da751\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] G:\Temp\.gradle\caches\transforms-4\087cdab7c8e620c4c70e4151fb03c9e0\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] G:\Temp\.gradle\caches\transforms-4\087cdab7c8e620c4c70e4151fb03c9e0\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] G:\Temp\.gradle\caches\transforms-4\bb8cf323bb1fabdea918d641733791d9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] G:\Temp\.gradle\caches\transforms-4\bb8cf323bb1fabdea918d641733791d9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\AndroidManifest.xml
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] G:\Temp\.gradle\caches\transforms-4\a1d6475431b58dc98d4268d04bf2f971\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.jnz.ai_meeting_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.jnz.ai_meeting_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
