1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jnz.ai_meeting_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="19"
9        android:targetSdkVersion="33" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:3:5-4:53
15-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:4:9-51
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:5:5-6:65
16-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:6:9-63
17    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
17-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:7:5-8:65
17-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:8:9-63
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:9:5-10:65
18-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:10:9-63
19    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
19-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:11:5-12:62
19-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:12:9-60
20    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
20-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:13:5-14:62
20-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:14:9-60
21    <uses-permission android:name="android.permission.NFC" /> <!-- 存储 -->
21-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:15:5-16:48
21-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:16:9-46
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:18:5-19:66
22-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:19:9-64
23    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 音视频 -->
23-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:20:5-21:67
23-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:21:9-65
24    <uses-permission android:name="android.permission.CAMERA" />
24-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:23:5-24:51
24-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:24:9-49
25    <uses-permission android:name="android.permission.RECORD_AUDIO" />
25-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:25:5-26:57
25-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:26:9-55
26    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
26-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:27:5-28:66
26-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:28:9-64
27    <uses-permission android:name="android.permission.MICROPHONE" />
27-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:29:5-30:55
27-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:30:9-53
28    <uses-permission android:name="android.permission.AUDIO_CAPTURE" />
28-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:31:5-32:58
28-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:32:9-56
29    <uses-permission android:name="android.permission.VIDEO_CAPTURE" />
29-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:33:5-34:58
29-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:34:9-56
30    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
30-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:35:5-36:72
30-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:36:9-70
31    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
31-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:37:5-38:65
31-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:38:9-63
32    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" /> <!-- 音视频扩展 -->
32-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:39:5-40:65
32-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:40:9-63
33    <uses-feature android:name="android.hardware.camera" />
33-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:42:5-43:49
33-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:43:9-47
34    <uses-feature android:name="android.hardware.camera.autofocus" />
34-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:44:5-45:59
34-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:45:9-57
35    <uses-feature android:name="android.hardware.microphone" /> <!-- 防止应用在后台被杀死 -->
35-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:46:5-47:53
35-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:47:9-51
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:49:5-50:54
36-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:50:9-52
37    <!--
38 Required to query activities that can process text, see:
39         https://developer.android.com/training/package-visibility?hl=en and
40         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
41
42         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
43    -->
44    <queries>
44-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:88:5-95:15
45        <intent>
45-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:89:9-94:18
46            <action android:name="android.intent.action.PROCESS_TEXT" />
46-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:90:13-91:68
46-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:91:17-66
47
48            <data android:mimeType="text/plain" />
48-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:92:13-93:48
48-->G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\AndroidManifest.xml:93:17-46
49        </intent>
50        <intent>
50-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
51            <action android:name="android.support.customtabs.action.CustomTabsService" />
51-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
51-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
52        </intent>
53    </queries>
54
55    <permission
55-->[androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
56        android:name="com.jnz.ai_meeting_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
56-->[androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
57        android:protectionLevel="signature" />
57-->[androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
58
59    <uses-permission android:name="com.jnz.ai_meeting_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
59-->[androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
59-->[androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
60
61    <application
62        android:name="android.app.Application"
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.9.0] G:\Temp\.gradle\caches\transforms-4\97bd1f780bbb39298a12511d0f8b7150\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
64        android:debuggable="true"
65        android:extractNativeLibs="true"
66        android:icon="@mipmap/ic_launcher"
67        android:label="AI会议记录"
68        android:usesCleartextTraffic="true" >
69
70        <!-- android:networkSecurityConfig="@xml/network_security_config"> -->
71        <activity
72            android:name="com.jnz.ai_meeting_app.MainActivity"
73            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
74            android:exported="true"
75            android:hardwareAccelerated="true"
76            android:launchMode="singleTop"
77            android:theme="@style/LaunchTheme"
78            android:windowSoftInputMode="adjustResize" >
79
80            <!--
81                 Specifies an Android theme to apply to this Activity as soon as
82                 the Android process has started. This theme is visible to the user
83                 while the Flutter UI initializes. After that, this theme continues
84                 to determine the Window background behind the Flutter UI.
85            -->
86            <meta-data
87                android:name="io.flutter.embedding.android.NormalTheme"
88                android:resource="@style/NormalTheme" />
89
90            <intent-filter>
91                <action android:name="android.intent.action.MAIN" />
92
93                <category android:name="android.intent.category.LAUNCHER" />
94            </intent-filter>
95        </activity>
96        <!--
97             Don't delete the meta-data below.
98             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
99        -->
100        <meta-data
101            android:name="flutterEmbedding"
102            android:value="2" />
103
104        <activity
104-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
105            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
105-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
106            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
106-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
107            android:exported="false"
107-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
108            android:theme="@style/AppTheme" />
108-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
109        <activity
109-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
110            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
110-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
111            android:exported="false"
111-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
112            android:theme="@style/ThemeTransparent" />
112-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
113        <activity
113-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
114            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
114-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
115            android:exported="false"
115-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
116            android:theme="@style/ThemeTransparent" />
116-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
117        <activity
117-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
118            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
118-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
119            android:exported="false"
119-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
120            android:launchMode="singleInstance"
120-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
121            android:theme="@style/ThemeTransparent" />
121-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
122        <activity
122-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
123            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
123-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
124            android:exported="false"
124-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
125            android:launchMode="singleInstance"
125-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
126            android:theme="@style/ThemeTransparent" />
126-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
127
128        <receiver
128-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
129            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
129-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
130            android:enabled="true"
130-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
131            android:exported="false" />
131-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
132
133        <meta-data
133-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
134            android:name="io.flutter.embedded_views_preview"
134-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
135            android:value="true" />
135-->[:flutter_inappwebview_android] G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
136
137        <provider
137-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
138            android:name="androidx.startup.InitializationProvider"
138-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
139            android:authorities="com.jnz.ai_meeting_app.androidx-startup"
139-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
140            android:exported="false" >
140-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
141            <meta-data
141-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
142                android:name="androidx.emoji2.text.EmojiCompatInitializer"
142-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
143                android:value="androidx.startup" />
143-->[androidx.emoji2:emoji2:1.2.0] G:\Temp\.gradle\caches\transforms-4\eace6ee8b4a97d30f083580faad93395\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
144            <meta-data
144-->[androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
145                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
145-->[androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
146                android:value="androidx.startup" />
146-->[androidx.lifecycle:lifecycle-process:2.4.1] G:\Temp\.gradle\caches\transforms-4\b3032c6b678e9e270d8500ec6f02e2a0\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
147        </provider>
148
149        <uses-library
149-->[androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:25:9-27:40
150            android:name="androidx.window.extensions"
150-->[androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:26:13-54
151            android:required="false" />
151-->[androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:27:13-37
152        <uses-library
152-->[androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:28:9-30:40
153            android:name="androidx.window.sidecar"
153-->[androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:29:13-51
154            android:required="false" />
154-->[androidx.window:window:1.0.0-beta04] G:\Temp\.gradle\caches\transforms-4\921d19833946acc3e7fa941a596218bc\transformed\jetified-window-1.0.0-beta04\AndroidManifest.xml:30:13-37
155    </application>
156
157</manifest>
