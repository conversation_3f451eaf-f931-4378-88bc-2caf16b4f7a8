<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI会议记录</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="./js/tailwindcss.js"></script>
    
    <!-- 自定义配置 -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        }
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'float': 'float 6s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(0)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/styles.css">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="./css/tailwindcss.css">
</head>
<body class="dark bg-dark-900 text-white min-h-screen font-sans overflow-hidden">
    <!-- 动态背景 -->
    <div class="fixed inset-0 z-0 overflow-hidden">
        <!-- 主渐变背景 -->
        <div class="absolute inset-0 bg-gradient-to-br from-dark-900 via-slate-900 to-dark-900"></div>

        <!-- 动态光晕背景 -->
        <div class="absolute inset-0">
            <!-- 大型光晕 -->
            <div class="absolute top-[5%] left-[3%] w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full filter blur-3xl animate-float opacity-40"></div>
            <div class="absolute top-[31%] right-[2%] w-80 h-80 bg-gradient-to-bl from-purple-500/25 to-pink-500/25 rounded-full filter blur-3xl animate-float opacity-35" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-[8%] left-[29%] w-72 h-72 bg-gradient-to-tr from-cyan-500/30 to-blue-500/30 rounded-full filter blur-3xl animate-float opacity-30" style="animation-delay: 3s;"></div>
            <div class="absolute bottom-[23%] right-[26%] w-64 h-64 bg-gradient-to-tl from-indigo-500/25 to-purple-500/25 rounded-full filter blur-3xl animate-float opacity-25" style="animation-delay: 2s;"></div>
            <div class="absolute top-[58%] left-[7%] w-56 h-56 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-full filter blur-3xl animate-float opacity-30" style="animation-delay: 4s;"></div>

            <!-- 中型光晕 -->
            <div class="absolute top-[47%] left-[19%] w-48 h-48 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full filter blur-2xl animate-pulse-slow opacity-50"></div>
            <div class="absolute top-[18%] right-[31%] w-40 h-40 bg-gradient-to-l from-purple-400/20 to-pink-400/20 rounded-full filter blur-2xl animate-pulse-slow opacity-45" style="animation-delay: 1.5s;"></div>
            <div class="absolute bottom-[35%] right-[12%] w-44 h-44 bg-gradient-to-tr from-yellow-400/15 to-orange-400/15 rounded-full filter blur-2xl animate-pulse-slow opacity-40" style="animation-delay: 2.5s;"></div>

            <!-- 随机分布的闪烁光点 -->
            <div class="absolute top-[12%] left-[8%] w-1 h-1 bg-blue-400 rounded-full animate-ping opacity-40" style="animation-delay: 0.3s;"></div>
            <div class="absolute top-[28%] left-[73%] w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping opacity-65" style="animation-delay: 1.2s;"></div>
            <div class="absolute top-[45%] right-[15%] w-0.5 h-0.5 bg-cyan-400 rounded-full animate-ping opacity-35" style="animation-delay: 2.1s;"></div>
            <div class="absolute bottom-[38%] left-[22%] w-2 h-2 bg-pink-400 rounded-full animate-ping opacity-55" style="animation-delay: 0.8s;"></div>
            <div class="absolute top-[67%] right-[42%] w-1 h-1 bg-indigo-400 rounded-full animate-ping opacity-45" style="animation-delay: 1.7s;"></div>
            <div class="absolute top-[15%] left-[55%] w-0.5 h-0.5 bg-emerald-400 rounded-full animate-ping opacity-30" style="animation-delay: 2.5s;"></div>
            <div class="absolute bottom-[22%] right-[8%] w-1.5 h-1.5 bg-yellow-400 rounded-full animate-ping opacity-50" style="animation-delay: 0.6s;"></div>
            <div class="absolute top-[82%] left-[35%] w-1 h-1 bg-rose-400 rounded-full animate-ping opacity-40" style="animation-delay: 1.9s;"></div>
            <div class="absolute top-[38%] left-[12%] w-0.5 h-0.5 bg-violet-400 rounded-full animate-ping opacity-60" style="animation-delay: 1.4s;"></div>
            <div class="absolute bottom-[15%] right-[65%] w-2 h-2 bg-teal-400 rounded-full animate-ping opacity-35" style="animation-delay: 2.8s;"></div>
            <div class="absolute top-[55%] right-[28%] w-1 h-1 bg-orange-400 rounded-full animate-ping opacity-50" style="animation-delay: 0.4s;"></div>
            <div class="absolute bottom-[45%] left-[68%] w-1.5 h-1.5 bg-lime-400 rounded-full animate-ping opacity-45" style="animation-delay: 2.3s;"></div>
            <div class="absolute top-[9%] right-[47%] w-0.5 h-0.5 bg-sky-400 rounded-full animate-ping opacity-55" style="animation-delay: 3.2s;"></div>
            <div class="absolute bottom-[62%] left-[84%] w-1 h-1 bg-fuchsia-400 rounded-full animate-ping opacity-40" style="animation-delay: 0.9s;"></div>
            <div class="absolute top-[73%] left-[6%] w-1.5 h-1.5 bg-amber-400 rounded-full animate-ping opacity-60" style="animation-delay: 1.6s;"></div>
            <div class="absolute bottom-[29%] right-[78%] w-0.5 h-0.5 bg-green-400 rounded-full animate-ping opacity-35" style="animation-delay: 2.7s;"></div>
            <div class="absolute top-[41%] left-[91%] w-1 h-1 bg-red-400 rounded-full animate-ping opacity-50" style="animation-delay: 0.7s;"></div>
            <div class="absolute bottom-[8%] left-[52%] w-2 h-2 bg-purple-400 rounded-full animate-ping opacity-45" style="animation-delay: 3.1s;"></div>
            <div class="absolute top-[86%] right-[19%] w-0.5 h-0.5 bg-cyan-400 rounded-full animate-ping opacity-40" style="animation-delay: 1.3s;"></div>
            <div class="absolute bottom-[71%] left-[37%] w-1 h-1 bg-pink-400 rounded-full animate-ping opacity-55" style="animation-delay: 2.4s;"></div>
            <div class="absolute top-[24%] right-[6%] w-1.5 h-1.5 bg-indigo-400 rounded-full animate-ping opacity-35" style="animation-delay: 0.5s;"></div>
        </div>

        <!-- 动态线条背景 -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="lineGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0.1" />
                    </linearGradient>
                    <linearGradient id="lineGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.1" />
                    </linearGradient>
                </defs>
                <path class="animate-draw-line" d="M0,100 Q400,50 800,200 T1600,150" stroke="url(#lineGradient1)" stroke-width="2" fill="none" opacity="0.6"/>
                <path class="animate-draw-line" d="M0,300 Q600,150 1200,400 T2400,250" stroke="url(#lineGradient2)" stroke-width="1.5" fill="none" opacity="0.4" style="animation-delay: 1s;"/>
                <path class="animate-draw-line" d="M0,500 Q300,350 600,600 T1200,450" stroke="url(#lineGradient1)" stroke-width="1" fill="none" opacity="0.3" style="animation-delay: 2s;"/>
            </svg>
        </div>

        <!-- 网格背景增强 -->
        <div class="absolute inset-0 opacity-8" style="background-image:
            radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.3) 1px, transparent 0),
            radial-gradient(circle at 20px 20px, rgba(139, 92, 246, 0.2) 1px, transparent 0);
            background-size: 40px 40px, 60px 60px;"></div>

        <!-- 动态几何形状 -->
        <div class="absolute inset-0 opacity-15">
            <!-- 旋转的六边形 -->
            <div class="absolute top-1/4 left-1/6 w-32 h-32 border border-blue-400/30 transform rotate-12 animate-spin-slow" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);"></div>
            <div class="absolute bottom-1/3 right-1/5 w-24 h-24 border border-purple-400/25 transform -rotate-45 animate-spin-reverse" style="clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);"></div>

            <!-- 浮动的三角形 -->
            <div class="absolute top-2/3 left-1/3 w-16 h-16 border border-cyan-400/30 transform rotate-45 animate-float" style="clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"></div>
            <div class="absolute top-1/5 right-1/4 w-12 h-12 border border-pink-400/25 transform -rotate-12 animate-float" style="clip-path: polygon(50% 0%, 0% 100%, 100% 100%); animation-delay: 1s;"></div>

            <!-- 脉冲圆环 -->
            <div class="absolute top-1/2 right-1/6 w-20 h-20 border-2 border-blue-400/20 rounded-full animate-ping"></div>
            <div class="absolute bottom-1/4 left-1/4 w-16 h-16 border border-purple-400/15 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
        </div>

        <!-- 光束效果 -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-blue-400/50 to-transparent animate-pulse"></div>
            <div class="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-purple-400/40 to-transparent animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/30 to-transparent animate-pulse" style="animation-delay: 2s;"></div>
        </div>
    </div>

    <!-- 离线状态提示 -->
    <div id="offline-banner" class="hidden bg-red-600 text-white text-center py-2 px-4 text-sm relative z-50">
        <i class="fas fa-wifi-slash mr-2"></i>
        网络连接已断开，会议功能暂时不可用
    </div>

    <!-- 主容器 -->
    <div class="flex h-screen overflow-hidden relative z-10">
        <!-- 左侧边栏 -->
        <aside id="sidebar" class="w-full md:w-80 bg-slate-600/95 backdrop-blur-lg border-r border-slate-400/80 flex flex-col transition-transform duration-300 ease-in-out transform md:translate-x-0 -translate-x-full fixed md:relative z-30 h-full shadow-2xl">
            <!-- 侧边栏头部 -->
            <div class="p-6 border-b border-slate-400/80 bg-slate-500/40">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-white flex items-center">
                        <i class="fas fa-history mr-3 text-primary-200"></i>
                        会议历史
                    </h2>
                    <button id="sidebar-close" class="md:hidden text-gray-100 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- 搜索和筛选区域 -->
            <div class="p-4 border-b border-slate-400/80 bg-slate-500/30">
                <div class="relative mb-3">
                    <input type="text" id="search-input" placeholder="搜索会议..."
                           class="w-full bg-slate-500/80 border border-slate-400/80 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-300 focus:border-transparent transition-all backdrop-blur-sm">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-100"></i>
                </div>

                <!-- 状态筛选 -->
                <div class="flex space-x-2">
                    <button class="filter-btn active px-3 py-1 rounded-full text-xs font-medium bg-primary-400 text-white shadow-md" data-filter="all">
                        全部
                    </button>
                    <button class="filter-btn px-3 py-1 rounded-full text-xs font-medium bg-slate-500/80 text-white hover:bg-slate-400/80 hover:text-white" data-filter="completed">
                        已完成
                    </button>
                    <button class="filter-btn px-3 py-1 rounded-full text-xs font-medium bg-slate-500/80 text-white hover:bg-slate-400/80 hover:text-white" data-filter="processing">
                        处理中
                    </button>
                </div>
            </div>

            <!-- 会议列表容器 -->
            <div class="flex-1 overflow-hidden">
                <div id="meeting-list" class="h-full overflow-y-auto custom-scrollbar">
                    <!-- 加载状态 -->
                    <div id="loading-skeleton" class="p-4 space-y-3">
                        <div class="animate-pulse">
                            <!-- 骨架屏 -->
                            <div class="bg-slate-600/70 rounded-lg p-4 mb-3">
                                <div class="flex items-start space-x-3">
                                    <div class="w-10 h-10 bg-slate-500/70 rounded-full"></div>
                                    <div class="flex-1 space-y-2">
                                        <div class="h-4 bg-slate-500/70 rounded w-3/4"></div>
                                        <div class="h-3 bg-slate-500/70 rounded w-1/2"></div>
                                        <div class="flex space-x-2">
                                            <div class="h-5 bg-slate-500/70 rounded-full w-16"></div>
                                            <div class="h-5 bg-slate-500/70 rounded w-12"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 会议列表项将通过JavaScript动态生成 -->
                    <div id="meeting-items" class="">


                        <!-- 示例会议项 -->
                        <div class="meeting-item p-4 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-all cursor-pointer" data-meeting-id="test-meeting-1">
                            <div class="flex items-start space-x-3">
                                <!-- 状态指示器 -->
                                <div class="flex-shrink-0 mt-1">
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                </div>

                                <!-- 会议信息 -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-medium text-white truncate mb-1 group-hover:text-primary-400 transition-colors">
                                        测试会议记录
                                    </h3>
                                    <p class="text-sm text-gray-400 mb-2 flex items-center">
                                        <i class="fas fa-calendar-alt mr-2 text-xs"></i>
                                        2025-07-26 14:30
                                    </p>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <span class="badge badge-success">
                                                已完成
                                            </span>
                                            <span class="text-xs text-gray-500 flex items-center">
                                                <i class="fas fa-clock mr-1"></i>
                                                01:23:45
                                            </span>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-500 group-hover:text-primary-400 transition-colors"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第二个示例会议项 -->
                        <div class="meeting-item p-4 rounded-lg border border-slate-600/30 hover:border-slate-500/50 transition-all cursor-pointer mt-3" data-meeting-id="test-meeting-2">
                            <div class="flex items-start space-x-3">
                                <!-- 状态指示器 -->
                                <div class="flex-shrink-0 mt-1">
                                    <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-clock text-white text-sm animate-spin"></i>
                                    </div>
                                </div>

                                <!-- 会议信息 -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-medium text-white truncate mb-1 group-hover:text-primary-400 transition-colors">
                                        处理中的会议
                                    </h3>
                                    <p class="text-sm text-gray-400 mb-2 flex items-center">
                                        <i class="fas fa-calendar-alt mr-2 text-xs"></i>
                                        2025-07-26 15:00
                                    </p>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <span class="badge badge-warning">
                                                处理中
                                            </span>
                                            <span class="text-xs text-gray-500">
                                                <span class="loading-dots">处理中</span>
                                            </span>
                                        </div>
                                        <i class="fas fa-chevron-right text-gray-500 group-hover:text-primary-400 transition-colors"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 空状态 -->
                    <div id="empty-state" class="hidden flex flex-col items-center justify-center h-64 text-gray-300 p-6">
                        <div class="w-16 h-16 bg-slate-600/70 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-calendar-times text-2xl text-gray-200"></i>
                        </div>
                        <p class="text-lg font-medium mb-2 text-gray-100">暂无会议记录</p>
                        <p class="text-sm text-center text-gray-200 leading-relaxed">
                            开始您的第一次AI会议记录<br>
                            体验智能转录的强大功能
                        </p>
                    </div>

                    <!-- 无搜索结果状态 -->
                    <div id="no-results-state" class="hidden flex flex-col items-center justify-center h-64 text-gray-300 p-6">
                        <div class="w-16 h-16 bg-slate-600/70 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-search text-2xl text-gray-200"></i>
                        </div>
                        <p class="text-lg font-medium mb-2 text-gray-100">未找到相关会议</p>
                        <p class="text-sm text-center text-gray-200">
                            尝试调整搜索关键词或筛选条件
                        </p>
                    </div>

                    <!-- 错误状态 -->
                    <div id="error-state" class="hidden flex flex-col items-center justify-center h-64 text-gray-300 p-6">
                        <div class="w-16 h-16 bg-slate-600/70 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-400"></i>
                        </div>
                        <p class="text-lg font-medium mb-2 text-gray-100">加载失败</p>
                        <p class="text-sm text-center text-gray-200 mb-4">
                            无法加载会议列表，请检查网络连接
                        </p>
                        <button id="retry-load" class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                            重新加载
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="flex-1 flex flex-col bg-transparent relative">
            <!-- 顶部标题栏 -->
            <header class="bg-gradient-to-r from-slate-800/95 via-slate-700/95 to-slate-800/95 backdrop-blur-lg border-b border-slate-500/50 shadow-2xl">
                <!-- 桌面端标题栏 -->
                <div class="hidden md:flex items-center justify-between p-6">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-microphone text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                                AI会议记录
                            </h1>
                            <p class="text-sm text-gray-400">智能语音转录与会议分析</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-slate-600/50">
                            <i class="fas fa-cog text-lg"></i>
                        </button>
                    </div>
                </div>

                <!-- 移动端标题栏 -->
                <div class="md:hidden flex items-center justify-between p-4">
                    <button id="sidebar-toggle" class="text-gray-200 hover:text-white transition-colors p-2 rounded-lg hover:bg-slate-600/50">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-microphone text-white text-sm"></i>
                        </div>
                        <h1 class="text-lg font-bold bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">AI会议记录</h1>
                    </div>
                    <div class="w-10"></div> <!-- 占位符保持居中 -->
                </div>
            </header>

            <!-- 主内容 -->
            <div class="flex-1 flex items-center justify-center p-4 md:p-6">
                <!-- 首页内容 -->
                <div id="home-content" class="text-center max-w-lg w-full animate-fade-in">
                    <!-- Logo/图标区域 -->
                    <div class="mb-8 md:mb-10">
                        <div class="relative mb-6 md:mb-8">
                            <!-- 主图标 -->
                            <div class="w-20 h-20 md:w-28 md:h-28 mx-auto bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 rounded-full flex items-center justify-center mb-4 md:mb-6 animate-pulse-slow glow-effect">
                                <i class="fas fa-microphone text-2xl md:text-4xl text-white"></i>
                            </div>
                            <!-- 装饰性圆环 -->
                            <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-24 md:w-32 md:h-32 border-2 border-primary-500 rounded-full opacity-20 animate-ping"></div>
                        </div>
                        
                        <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 md:mb-4 gradient-text">
                            AI会议记录
                        </h1>
                        <p class="text-gray-400 text-lg md:text-xl leading-relaxed">
                            智能记录每一个重要时刻
                        </p>
                    </div>

                    <!-- 开始会议按钮 -->
                    <div class="space-y-4 md:space-y-6 mb-12 md:mb-16 relative" style="margin-bottom:5rem">
                        <!-- 按钮发光背景 -->
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-500/30 to-primary-700/30 rounded-2xl filter blur-xl animate-pulse-slow"></div>

                        <!-- 主按钮 -->
                        <button id="start-meeting-btn" class="relative btn-primary w-full bg-gradient-to-r from-primary-600 via-primary-500 to-primary-700 hover:from-primary-700 hover:via-primary-600 hover:to-primary-800 text-white font-bold py-4 md:py-5 px-8 md:px-10 rounded-2xl text-lg md:text-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl shadow-lg ripple-effect glow-pulse border border-primary-400/30">
                            <!-- 按钮内部光效 -->
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- 按钮内容 -->
                            <div class="relative flex items-center justify-center">
                                <div class="relative">
                                    <i class="fas fa-play mr-3 md:mr-4 text-base md:text-lg drop-shadow-lg"></i>
                                    <!-- 图标发光效果 -->
                                    <div class="absolute inset-0 fas fa-play mr-3 md:mr-4 text-base md:text-lg text-white/50 filter blur-sm"></div>
                                </div>
                                <span class="drop-shadow-lg">开始会议</span>
                            </div>

                            <!-- 按钮边缘光点 -->
                            <div class="absolute top-2 left-4 w-1 h-1 bg-white/60 rounded-full animate-ping"></div>
                            <div class="absolute bottom-2 right-4 w-0.5 h-0.5 bg-cyan-300/50 rounded-full animate-pulse"></div>
                        </button>

                        <!-- 装饰性光线 -->
                        <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-32 h-0.5 bg-gradient-to-r from-transparent via-primary-400/50 to-transparent animate-pulse"></div>
                        <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-0.5 bg-gradient-to-r from-transparent via-purple-400/40 to-transparent animate-pulse" style="animation-delay: 0.5s;"></div>
                    </div>
                    
                    <!-- 功能特性展示 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                        <!-- 实时转录卡片 -->
                        <div class="relative group">
                            <!-- 卡片发光背景 -->
                            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl filter blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <div class="relative flex flex-col items-center p-3 md:p-4 rounded-xl glass-effect hover:bg-dark-700/30 transition-all duration-300 card-hover border border-blue-500/20 hover:border-blue-400/40">
                                <!-- 图标容器 -->
                                <div class="relative mb-2 md:mb-3">
                                    <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center shadow-lg">
                                        <i class="fas fa-microphone-alt text-white text-sm md:text-lg drop-shadow-sm"></i>
                                    </div>
                                    <!-- 图标光环 -->
                                    <div class="absolute inset-0 w-10 h-10 md:w-12 md:h-12 bg-blue-400/30 rounded-full"></div>
                                </div>

                                <span class="text-white font-medium mb-1 text-sm md:text-base">实时转录</span>
                                <span class="text-gray-400 text-xs md:text-sm text-center">高精度语音识别</span>

                                <!-- 底部装饰线 -->
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                        </div>

                        <!-- 说话人识别卡片 -->
                        <div class="relative group">
                            <!-- 卡片发光背景 -->
                            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl filter blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <div class="relative flex flex-col items-center p-3 md:p-4 rounded-xl glass-effect hover:bg-dark-700/30 transition-all duration-300 card-hover border border-purple-500/20 hover:border-purple-400/40">
                                <!-- 图标容器 -->
                                <div class="relative mb-2 md:mb-3">
                                    <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
                                        <i class="fas fa-users text-white text-sm md:text-lg drop-shadow-sm"></i>
                                    </div>
                                    <!-- 图标光环 -->
                                    <div class="absolute inset-0 w-10 h-10 md:w-12 md:h-12 bg-purple-400/30 rounded-full" style="animation-delay: 0.5s;"></div>
                                </div>

                                <span class="text-white font-medium mb-1 text-sm md:text-base">角色识别</span>
                                <span class="text-gray-400 text-xs md:text-sm text-center">智能区分发言人</span>

                                <!-- 底部装饰线 -->
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                        </div>

                        <!-- AI智能摘要卡片 -->
                        <div class="relative group">
                            <!-- 卡片发光背景 -->
                            <div class="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl filter blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <div class="relative flex flex-col items-center p-3 md:p-4 rounded-xl glass-effect hover:bg-dark-700/30 transition-all duration-300 card-hover border border-green-500/20 hover:border-green-400/40">
                                <!-- 图标容器 -->
                                <div class="relative mb-2 md:mb-3">
                                    <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                                        <i class="fas fa-brain text-white text-sm md:text-lg drop-shadow-sm"></i>
                                    </div>
                                    <!-- 图标光环 -->
                                    <div class="absolute inset-0 w-10 h-10 md:w-12 md:h-12 bg-green-400/30 rounded-full" style="animation-delay: 1s;"></div>
                                </div>

                                <span class="text-white font-medium mb-1 text-sm md:text-base">智能摘要</span>
                                <span class="text-gray-400 text-xs md:text-sm text-center">自动生成会议纪要</span>

                                <!-- 底部装饰线 -->
                                <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 会议进行中内容 (初始隐藏) -->
                <div id="meeting-content" class="hidden w-full h-full flex flex-col animate-fade-in p-2 md:p-4">
                    <!-- 顶部状态栏 - 优化布局 -->
                    <div class="flex flex-col p-3 md:p-4 bg-slate-800/50 backdrop-blur-sm rounded-xl mb-2 md:mb-3">
                        <!-- 第一行：录制状态 + 会议标题 + 音频波形 -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <!-- 录制状态指示器 -->
                                <div class="relative">
                                    <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center recording-pulse">
                                        <i class="fas fa-circle text-white text-xs md:text-sm"></i>
                                    </div>
                                    <div class="absolute top-0 left-0 w-8 h-8 md:w-10 md:h-10 border border-red-400 rounded-full opacity-30 animate-ping"></div>
                                </div>

                                <div>
                                    <h2 class="text-base md:text-lg font-bold text-white">会议进行中</h2>
                                    <p class="text-gray-400 text-xs md:text-sm">正在实时转录...</p>
                                </div>
                            </div>

                            <!-- 音频波形 -->
                            <div class="flex items-center space-x-2">
                                <div class="audio-wave">
                                    <span class="bg-gradient-to-t from-primary-600 to-primary-400"></span>
                                    <span class="bg-gradient-to-t from-primary-600 to-primary-400"></span>
                                    <span class="bg-gradient-to-t from-primary-600 to-primary-400"></span>
                                    <span class="bg-gradient-to-t from-primary-600 to-primary-400"></span>
                                    <span class="bg-gradient-to-t from-primary-600 to-primary-400"></span>
                                </div>
                                <span class="text-gray-400 text-xs md:text-sm hidden md:inline">音频输入</span>
                            </div>
                        </div>

                        <!-- 第二行：计时器 + 统计信息 -->
                        <div class="flex items-center justify-between">
                            <!-- 计时器 - 时长文本在左侧 -->
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-400">会议时长</span>
                                <div class="timer-display text-lg md:text-xl font-mono text-primary-400 glow-effect" id="meeting-timer">
                                    00:00:00
                                </div>
                            </div>

                            <!-- 统计信息 -->
                            <div class="flex items-center space-x-2 md:space-x-4">
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-users text-blue-400 text-xs md:text-sm"></i>
                                    <span class="text-white font-bold text-xs md:text-base">1</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <i class="fas fa-file-alt text-green-400 text-xs md:text-sm"></i>
                                    <span class="text-white font-bold text-xs md:text-base" id="word-count">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时转录显示区域 - 最大化高度 -->
                    <div class="flex-1 flex flex-col min-h-0 mb-2 md:mb-3">
                        <div class="transcript-area glass-effect border border-primary-500/30 rounded-xl md:rounded-2xl p-3 md:p-4 flex-1 flex flex-col min-h-0">
                            <div class="flex items-center justify-between mb-2 md:mb-3">
                                <h3 class="text-base md:text-lg font-semibold text-white flex items-center">
                                    <i class="fas fa-microphone-alt mr-2 text-primary-400"></i>
                                    实时转录中
                                </h3>
                            </div>

                            <!-- 转录内容区域 - 优化滚动和显示 -->
                            <div id="meeting-transcript-content" class="transcript-text text-left space-y-2 md:space-y-3 flex-1 overflow-y-auto custom-scrollbar p-2 md:p-3 bg-slate-900/30 rounded-lg border border-slate-700/50">
                                <!-- 示例转录内容 -->
                                <div class="text-gray-300 text-sm md:text-base leading-relaxed">
                                    <p class="mb-2">等待语音输入...</p>
                                    <p class="text-gray-500 text-xs md:text-sm">开始说话后，转录内容将在此处实时显示</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部控制区域 - 简化布局 -->
                    <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-3 md:p-4">
                        <!-- 会议控制按钮区域 - 移动端优化 -->
                        <div class="flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-4">
                            <!-- 移动端主要按钮 -->
                            <div class="flex items-center space-x-3 md:hidden w-full">
                                <button id="mute-btn-mobile" class="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg flex items-center justify-center">
                                    <i class="fas fa-microphone text-sm mr-2"></i>
                                    <span class="text-sm">静音</span>
                                </button>
                                <button id="end-meeting-btn-mobile" class="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 shadow-lg flex items-center justify-center">
                                    <i class="fas fa-stop text-sm mr-2"></i>
                                    <span class="text-sm">结束会议</span>
                                </button>
                            </div>

                            <!-- 桌面端按钮 -->
                            <div class="hidden md:flex items-center space-x-4">
                                <button id="mute-btn" class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-microphone text-lg"></i>
                                </button>

                                <button id="pause-meeting-btn" class="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-pause mr-2"></i>
                                    暂停录制
                                </button>

                                <button id="end-meeting-btn" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-stop mr-2"></i>
                                    结束会议
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 会议详情页内容 (初始隐藏) -->
                <div id="detail-content" class="hidden w-full h-full flex flex-col animate-fade-in">


                    <!-- 会议基本信息 - 紧凑设计 -->
                    <div class="glass-effect rounded-xl p-4 mb-4 border border-primary-500/20">
                        <!-- 顶部操作栏 -->
                        <div class="flex items-center justify-between mb-3">
                            <button id="back-to-home" class="flex items-center text-gray-300 hover:text-white transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>
                                <span class="hidden sm:inline">返回首页</span>
                            </button>

                            <div class="flex items-center space-x-2">
                                <!-- 刷新按钮 -->
                                <button id="refresh-detail" class="p-2 text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-slate-700/50" title="刷新">
                                    <i class="fas fa-sync-alt text-sm"></i>
                                </button>
                                <!-- 分享按钮 -->
                                <button id="share-meeting" class="p-2 text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-slate-700/50" title="分享">
                                    <i class="fas fa-share-alt text-sm"></i>
                                </button>
                                <!-- 下载按钮 -->
                                <button id="download-meeting" class="p-2 text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-slate-700/50" title="下载">
                                    <i class="fas fa-download text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1 min-w-0">
                                <h1 id="meeting-title" class="text-lg sm:text-xl font-bold text-white mb-2 truncate">
                                    会议加载中...
                                </h1>
                                <div class="flex flex-wrap items-center gap-3 text-xs sm:text-sm text-gray-300">
                                    <span class="flex items-center">
                                        <i class="fas fa-calendar-alt mr-1 text-primary-400"></i>
                                        <span id="meeting-date">--</span>
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-clock mr-1 text-primary-400"></i>
                                        <span id="meeting-duration">--</span>
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-users mr-1 text-primary-400"></i>
                                        <span id="meeting-participants">--</span>
                                    </span>
                                </div>
                            </div>

                            <div class="ml-4">
                                <span id="meeting-status" class="badge badge-processing text-xs px-3 py-1">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>
                                    加载中
                                </span>
                            </div>
                        </div>

                        <!-- 音频播放器 - 简化设计 -->
                        <div class="bg-slate-800/50 rounded-lg p-3 border border-slate-600/30">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-sm font-semibold text-white flex items-center">
                                    <i class="fas fa-headphones mr-2 text-primary-400"></i>
                                    会议录音
                                </h3>
                            </div>

                            <div class="flex items-center space-x-3">
                                <button id="play-pause-btn" class="w-8 h-8 bg-gradient-to-r from-primary-600 to-primary-700 rounded-full flex items-center justify-center text-white hover:from-primary-700 hover:to-primary-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                    <i class="fas fa-play text-sm"></i>
                                </button>

                                <div class="flex-1">
                                    <div class="relative">
                                        <div class="w-full h-1.5 bg-slate-700 rounded-full cursor-pointer" id="audio-progress-bar">
                                            <div id="audio-progress" class="h-1.5 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full transition-all" style="width: 0%"></div>
                                        </div>
                                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                                            <span id="audio-current-time">00:00</span>
                                            <span id="audio-total-time">--:--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 隐藏的音频元素 -->
                            <audio id="meeting-audio" preload="metadata" class="hidden"></audio>
                        </div>
                    </div>

                    <!-- 内容区域 - 新的布局设计 -->
                    <div class="flex-1 flex flex-col lg:flex-row gap-4 min-h-0">
                        <!-- 主要内容区域 -->
                        <div class="flex-1 flex flex-col space-y-4 min-h-0">
                            <!-- AI摘要 - 紧凑设计 -->
                            <div class="glass-effect rounded-xl p-4 border border-green-500/20">
                                <div class="flex items-center justify-between mb-3">
                                    <h2 class="text-lg font-bold text-white flex items-center">
                                        <i class="fas fa-brain mr-2 text-green-400"></i>
                                        会议信息
                                    </h2>
                                    <div class="flex items-center space-x-4 text-sm text-gray-300">
                                        <span class="flex items-center" title="转录字数">
                                            <i class="fas fa-file-text mr-1 text-green-400"></i>
                                            <span id="summary-word-count">--</span>字
                                        </span>
                                        <span class="flex items-center"  title="发言次数">
                                            <i class="fas fa-comments mr-1 text-green-400"></i>
                                            <span id="summary-speech-count">--</span>次
                                        </span>
                                    </div>
                                </div>

                                <div id="meeting-summary" class="space-y-3">
                                    <!-- 加载状态 -->
                                    <div class="flex items-center justify-center py-8 text-gray-400">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        正在生成摘要...
                                    </div>
                                </div>
                            </div>


                        </div>

                        <!-- 会议记录侧边栏 -->
                        <div class="w-full lg:w-96 flex flex-col min-h-0">
                            <!-- 侧边栏头部 -->
                            <div class="flex items-center justify-between p-3 bg-slate-800/50 rounded-t-xl border-b border-slate-600/30">
                                <h3 class="text-lg font-bold text-white flex items-center">
                                    <i class="fas fa-comments mr-2 text-blue-400"></i>
                                    完整记录
                                </h3>
                                <button id="toggle-transcript-sidebar" class="lg:hidden p-1 text-gray-400 hover:text-white transition-colors">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <!-- 记录内容区域 -->
                            <div class="flex-1 glass-effect rounded-b-xl border border-blue-500/20 border-t-0 flex flex-col min-h-0">
                                <!-- 加载状态 -->
                                <div id="transcript-loading" class="flex items-center justify-center py-8 text-gray-400">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>
                                    正在加载转录内容...
                                </div>

                                <!-- 转录内容 -->
                                <div id="transcript-content" class="hidden flex-1 overflow-y-auto custom-scrollbar p-4 space-y-3">
                                    <!-- 转录内容将通过JavaScript动态加载 -->
                                </div>

                                <!-- 空状态 -->
                                <div id="transcript-empty" class="hidden flex flex-col items-center justify-center py-8 text-gray-400">
                                    <i class="fas fa-microphone-slash text-3xl mb-3 opacity-50"></i>
                                    <p class="text-center">会议内容为空</p>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </main>
    </div>

    <!-- 遮罩层 (移动端侧边栏) -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden hidden"></div>

    <!-- JavaScript文件 -->
    <script src="js/config.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
