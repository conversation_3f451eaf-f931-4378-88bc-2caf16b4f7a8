# This is a generated file; do not edit or check into version control.
flutter_inappwebview=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_inappwebview-6.0.0\\
flutter_inappwebview_android=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_inappwebview_android-1.0.13\\
flutter_inappwebview_ios=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_inappwebview_ios-1.0.13\\
flutter_inappwebview_macos=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_inappwebview_macos-1.0.11\\
flutter_inappwebview_web=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_inappwebview_web-1.0.8\\
flutter_native_splash=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\flutter_native_splash-2.4.0\\
path_provider=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider-2.1.2\\
path_provider_android=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_android-2.2.4\\
path_provider_foundation=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_foundation-2.4.1\\
path_provider_linux=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_linux-2.2.1\\
path_provider_windows=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\path_provider_windows-2.3.0\\
permission_handler=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\permission_handler-11.3.1\\
permission_handler_android=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\permission_handler_android-12.0.13\\
permission_handler_apple=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\permission_handler_apple-9.4.7\\
permission_handler_html=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\permission_handler_html-0.1.3+5\\
permission_handler_windows=G:\\Temp\\Flutter\\.pub-cache\\hosted\\pub.flutter-io.cn\\permission_handler_windows-0.2.1\\
