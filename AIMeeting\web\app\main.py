import httpx
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, Depends, HTTPException, Security, APIRouter, Query
from fastapi.security.api_key import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from . import crud, models
from .database import SessionLocal, engine, get_db
from .config import settings
from .aliyun_meeting import start_realtime_meeting, end_realtime_meeting, get_realtime_meeting

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 应用启动时，根据ORM模型创建数据库表
models.Base.metadata.create_all(bind=engine)

# --- 后台轮询任务 ---
# （这部分函数代码保持不变）


async def poll_and_process_results():
    """定期执行的后台任务，用于检查正在处理中的任务并获取结果"""
    logger.info("后台轮询：开始检查'PROCESSING'状态的任务...")
    db = SessionLocal()  # 为后台任务创建独立的DB会话
    try:
        tasks_to_process = crud.get_tasks_by_status(
            db, models.TaskStatusEnum.PROCESSING)
        if not tasks_to_process:
            logger.info("后台轮询：没有需要处理的任务。")
            return

        async with httpx.AsyncClient() as client:
            for task in tasks_to_process:
                logger.info(f"后台轮询：正在检查 task_id: {task.task_id}")

                try:
                    # 调用阿里云API获取任务结果
                    data = get_realtime_meeting(task.task_id)

                    if data.get("Code") != "0":
                        logger.error(
                            f"阿里云API在获取任务 {task.task_id} 结果时出错: {data.get('Message')}")
                        continue

                    task_status = data.get("Data", {}).get("TaskStatus")
                    if task_status == "COMPLETED":
                        logger.info(f"任务 {task.task_id} 已完成，正在获取并保存结果。")
                        results_data = data["Data"]
                        full_results = {
                            "OutputMp3Path": results_data.get("OutputMp3Path", "")
                        }

                        # 下载所有结果URL中的JSON内容
                        result_urls = results_data.get("Result", {})
                        for key, url in result_urls.items():
                            try:
                                full_results[key+"Url"] = url
                                result_resp = await client.get(url, timeout=10.0)
                                result_resp.raise_for_status()
                                # 将下载的JSON内容存入字典
                                full_results[key] = result_resp.json()
                            except httpx.RequestError as exc:
                                logger.error(f"下载结果失败 {key} from {url}: {exc}")

                        # 将所有下载好的结果保存到数据库
                        crud.save_final_results(db, task.task_id, full_results)
                        logger.info(f"成功保存任务 {task.task_id} 的结果。")

                except Exception as exc:
                    logger.error(f"轮询任务 {task.task_id} 时发生错误: {exc}")

    finally:
        db.close()


# --- 应用生命周期管理器 (Lifespan) ---
# 创建一个调度器实例
scheduler = AsyncIOScheduler()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时执行的代码
    logger.info("应用启动，启动后台轮询调度器...")
    scheduler.add_job(poll_and_process_results, 'interval',
                      seconds=settings.POLLING_INTERVAL_SECONDS)
    scheduler.start()

    yield  # 应用在此处运行

    # 应用关闭时执行的代码
    logger.info("应用关闭，正在安全关闭调度器...")
    scheduler.shutdown()

# FastAPI应用实例将在后面创建


# --- API认证 ---
api_key_header = APIKeyHeader(name="Authorization", auto_error=False)


async def get_api_key(api_key: str = Security(api_key_header)):
    """依赖项：验证客户端提供的API令牌"""
    if api_key == settings.SVC_AUTH:
        return api_key
    else:
        raise HTTPException(
            status_code=401,
            detail="无效或缺失的API令牌",
        )

# --- API路由 ---
router = APIRouter(prefix="/api/v1", dependencies=[Depends(get_api_key)])


@router.post("/meetings", summary="创建实时会议任务")
async def create_meeting(db: Session = Depends(get_db)):
    """
    创建一个新的实时会议任务。
    客户端无需传入参数，所有配置在服务端预设。
    """
    try:
        data = start_realtime_meeting()
        if data.get("Code") != "0":
            return {"code": 502, "msg": f"阿里云API错误: {data.get('Message')}"}

        # 在本地数据库中创建任务记录
        created_task = crud.create_meeting_task(db, task_data=data["Data"])

        return {
            "code": 200,
            "taskId": created_task.task_id,
            "meetingJoinUrl": created_task.meeting_join_url
        }
    except Exception as e:
        logger.error(f"创建会议任务失败: {e}")
        return {"code": 500, "msg": "服务器内部错误"}


@router.post("/meetings/{task_id}/stop", summary="结束实时会议任务")
async def stop_meeting(task_id: str, db: Session = Depends(get_db)):
    """
    通知服务指定任务的音频流已发送完毕。
    此操作会将任务状态变为'PROCESSING'，并由后台开始轮询结果。
    """
    try:
        db_task = crud.get_task_by_id(db, task_id)
        if not db_task:
            return {"code": 404, "msg": "未找到对应的会议记录"}

        data = end_realtime_meeting(task_id)
        if data.get("Code") != "0":
            return {"code": 502, "msg": f"阿里云API错误: {data.get('Message')}"}

        # 更新本地数据库任务状态
        crud.update_task_status(db, task_id, models.TaskStatusEnum.PROCESSING)

        return {"code": 200}
    except Exception as e:
        logger.error(f"结束会议任务失败: {e}")
        return {"code": 500, "msg": "服务器内部错误"}


@router.get("/meetings", summary="查询实时会议任务列表")
def list_meetings(skip: int = Query(0, ge=0), limit: int = Query(20, gt=0, le=100), db: Session = Depends(get_db)):
    """
    分页获取所有任务的列表。
    """
    try:
        tasks = crud.get_tasks(db, skip=skip, limit=limit)

        # 转换为设计文档要求的格式
        data = []
        for task in tasks:
            title = "会议记录"  # 默认标题
            duration_seconds = None

            # 如果有摘要，使用摘要标题
            if task.summarization and task.summarization.paragraph_title:
                title = task.summarization.paragraph_title
            else:
                # 否则使用创建时间格式
                title = f"会议 - {task.created_at.strftime('%Y-%m-%d %H:%M')}"

            # 如果有转录，计算时长
            if task.transcription and task.transcription.audio_duration_ms:
                duration_seconds = task.transcription.audio_duration_ms // 1000

            data.append({
                "taskId": task.task_id,
                "title": title,
                "taskStatus": task.task_status.value,
                "createdAt": task.created_at.isoformat() + "Z",
                "durationInSeconds": duration_seconds
            })

        return {"code": 200, "data": data}
    except Exception as e:
        logger.error(f"获取会议列表失败: {e}")
        return {"code": 500, "msg": "服务器内部错误"}


@router.get("/meetings/{task_id}", summary="获取会议任务结果")
def get_meeting_details(task_id: str, db: Session = Depends(get_db)):
    """
    获取单个任务的完整详情，包括所有已处理完成的结果。
    """
    try:
        db_task = crud.get_task_by_id(db, task_id)
        if db_task is None:
            return {"code": 404, "msg": "未找到对应的会议记录"}

        # 构建响应数据
        data = {
            "taskId": db_task.task_id,
            "taskStatus": db_task.task_status.value,
            "createdAt": db_task.created_at.isoformat() + "Z",
            "audioFileUrl": db_task.output_mp3_path,
            "durationInSeconds": None
        }

        # 添加摘要信息
        if db_task.summarization:
            data["summary"] = {
                "title": db_task.summarization.paragraph_title,
                "paragraph": db_task.summarization.paragraph_summary,
                "conversational": db_task.summarization.conversational_summary or []
            }

        # 添加转录信息
        if db_task.transcription:
            data["durationInSeconds"] = db_task.transcription.audio_duration_ms // 1000 if db_task.transcription.audio_duration_ms else None

            # 转换转录格式
            transcripts = []
            if db_task.transcription.paragraphs:
                for paragraph in db_task.transcription.paragraphs:
                    speaker_id = paragraph.get("SpeakerId", "1")
                    words = paragraph.get("Words", [])

                    if words:
                        # 合并同一段落的文字
                        text = "".join([word.get("Text", "")
                                       for word in words])
                        start_time = words[0].get("Start", 0)
                        end_time = words[-1].get("End", 0)

                        transcripts.append({
                            "speakerId": speaker_id,
                            "text": text,
                            "startTimeMs": start_time,
                            "endTimeMs": end_time
                        })

            data["transcripts"] = transcripts

        # 添加会议助手信息
        if db_task.assistance:
            data["assistance"] = {
                "keywords": db_task.assistance.keywords or [],
                "classifications": db_task.assistance.classifications or {}
            }

        return {"code":200, "data":data}
    except Exception as e:
        logger.error(f"获取会议详情失败: {e}")
        return {"code": 500, "msg": "服务器内部错误"}


# 创建FastAPI应用实例
app = FastAPI(
    title="AI会议记录API",
    description="基于阿里云听悟的实时会议转录和分析服务",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(router)
