<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res"><file name="floating_action_mode_shape" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\drawable\floating_action_mode_shape.xml" qualifiers="" type="drawable"/><file name="activity_web_view" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\layout\activity_web_view.xml" qualifiers="" type="layout"/><file name="chrome_custom_tabs_layout" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\layout\chrome_custom_tabs_layout.xml" qualifiers="" type="layout"/><file name="floating_action_mode" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\layout\floating_action_mode.xml" qualifiers="" type="layout"/><file name="floating_action_mode_item" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\layout\floating_action_mode_item.xml" qualifiers="" type="layout"/><file name="menu_main" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\values\strings.xml" qualifiers=""><string name="action_go_back">Go Back</string><string name="action_go_forward">Go Forward</string><string name="action_reload">Reload</string><string name="action_share">Share</string><string name="action_close">Close</string><string name="menu_search">Search</string></file><file path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\values\styles.xml" qualifiers=""><style name="InAppWebViewTheme" parent="Theme.AppCompat">

    </style><style name="AppTheme" parent="Theme.AppCompat.Light">

    </style><style name="ThemeTransparent" parent="android:Theme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style></file><file name="provider_paths" path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\res\xml\provider_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>