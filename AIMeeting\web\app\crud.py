from sqlalchemy.orm import Session, joinedload
from . import models, schemas
from typing import List

# 通过ID获取单个任务，并预加载所有关联数据以提高性能
def get_task_by_id(db: Session, task_id: str) -> models.MeetingTask:
    return db.query(models.MeetingTask).options(
        joinedload(models.MeetingTask.transcription),
        joinedload(models.MeetingTask.summarization),
        joinedload(models.MeetingTask.assistance)
    ).filter(models.MeetingTask.task_id == task_id).first()

# 分页获取任务列表
def get_tasks(db: Session, skip: int = 0, limit: int = 20) -> List[models.MeetingTask]:
    return db.query(models.MeetingTask).order_by(models.MeetingTask.id.desc()).offset(skip).limit(limit).all()

# 获取任务总数
def get_task_count(db: Session) -> int:
    return db.query(models.MeetingTask).count()

# 根据状态获取任务列表（供后台轮询器使用）
def get_tasks_by_status(db: Session, status: models.TaskStatusEnum) -> List[models.MeetingTask]:
    return db.query(models.MeetingTask).filter(models.MeetingTask.task_status == status).all()

# 在数据库中创建一个新任务
def create_meeting_task(db: Session, task_data: dict) -> models.MeetingTask:
    db_task = models.MeetingTask(
        task_id=task_data["TaskId"],
        task_key=task_data.get("TaskKey"),
        task_status=models.TaskStatusEnum.NEW,
        meeting_join_url=task_data["MeetingJoinUrl"]
    )
    db.add(db_task)
    db.commit()
    db.refresh(db_task)
    return db_task

# 更新任务状态
def update_task_status(db: Session, task_id: str, status: models.TaskStatusEnum):
    db_task = get_task_by_id(db, task_id)
    if db_task:
        db_task.task_status = status
        db.commit()
        db.refresh(db_task)
    return db_task
    
# 将最终处理结果保存到数据库
def save_final_results(db: Session, task_id: str, results: dict):
    db_task = get_task_by_id(db, task_id)
    if not db_task:
        return
        
    # 更新主任务表
    db_task.output_mp3_path = results.get("OutputMp3Path")
    db_task.meeting_assistance_url = results.get("MeetingAssistanceUrl")
    db_task.transcription_url = results.get("TranscriptionUrl")
    db_task.summarization_url = results.get("SummarizationUrl")
    
    # 在关联子表中创建记录
    if results.get("Transcription"):
        db_transcription = models.Transcription(
            task_id=task_id,
            audio_duration_ms=results["Transcription"]["Transcription"]["AudioInfo"].get("Duration"),
            paragraphs=results["Transcription"]["Transcription"].get("Paragraphs"),
            audio_segments=results["Transcription"]["Transcription"].get("AudioSegments")
        )
        db.add(db_transcription)

    if results.get("Summarization"):
        db_summarization = models.Summarization(
            task_id=task_id,
            paragraph_title=results["Summarization"]["Summarization"].get("ParagraphTitle"),
            paragraph_summary=results["Summarization"]["Summarization"].get("ParagraphSummary"),
            conversational_summary=results["Summarization"]["Summarization"].get("ConversationalSummary")
        )
        db.add(db_summarization)
        
    if results.get("MeetingAssistance"):
        db_assistance = models.MeetingAssistance(
            task_id=task_id,
            keywords=results["MeetingAssistance"]["MeetingAssistance"].get("Keywords"),
            classifications=results["MeetingAssistance"]["MeetingAssistance"].get("Classifications")
        )
        db.add(db_assistance)
        
    # 最后，更新任务状态为完成
    db_task.task_status = models.TaskStatusEnum.COMPLETED
    db.commit()