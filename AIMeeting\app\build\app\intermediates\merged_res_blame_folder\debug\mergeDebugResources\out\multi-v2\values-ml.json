{"logs": [{"outputFile": "com.jnz.ai_meeting_app-mergeDebugResources-4:/values-ml/values-ml.xml", "map": [{"source": "G:\\Temp\\.gradle\\caches\\transforms-4\\97bd1f780bbb39298a12511d0f8b7150\\transformed\\core-1.9.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3364", "endColumns": "100", "endOffsets": "3460"}}, {"source": "G:\\Temp\\.gradle\\caches\\transforms-4\\103df599988d4da89a096268f4456e78\\transformed\\appcompat-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,3281", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,3359"}}, {"source": "G:\\Temp\\.gradle\\caches\\transforms-4\\7d99e21d036517c2a918542a740b3cf7\\transformed\\browser-1.6.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2854,2963,3066,3177", "endColumns": "108,102,110,103", "endOffsets": "2958,3061,3172,3276"}}]}]}