{"logs": [{"outputFile": "com.jnz.ai_meeting_app-mergeDebugResources-4:/values-my/values-my.xml", "map": [{"source": "G:\\Temp\\.gradle\\caches\\transforms-4\\97bd1f780bbb39298a12511d0f8b7150\\transformed\\core-1.9.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "34", "startColumns": "4", "startOffsets": "3389", "endColumns": "100", "endOffsets": "3485"}}, {"source": "G:\\Temp\\.gradle\\caches\\transforms-4\\103df599988d4da89a096268f4456e78\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,3303", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,3384"}}, {"source": "G:\\Temp\\.gradle\\caches\\transforms-4\\7d99e21d036517c2a918542a740b3cf7\\transformed\\browser-1.6.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,106", "endOffsets": "159,265,380,487"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "2866,2975,3081,3196", "endColumns": "108,105,114,106", "endOffsets": "2970,3076,3191,3298"}}]}]}