// AudioWorklet处理器 - 用于音频录制和处理
class AudioRecorderProcessor extends AudioWorkletProcessor {
    constructor() {
        super();
        
        // 音频数据缓冲区
        this.audioData = {
            size: 0,
            buffer: [],
            inputSampleRate: 48000,
            inputSampleBits: 16,
            outputSampleRate: 16000,
            outputSampleBits: 16
        };
        
        // 监听主线程消息
        this.port.onmessage = (event) => {
            if (event.data.command === 'clear') {
                this.clearBuffer();
            }
        };
    }
    
    process(inputs, outputs, parameters) {
        const input = inputs[0];
        
        if (input && input.length > 0) {
            const inputChannel = input[0];
            
            if (inputChannel && inputChannel.length > 0) {
                // 将音频数据添加到缓冲区
                this.audioData.buffer.push(new Float32Array(inputChannel));
                this.audioData.size += inputChannel.length;
                
                // 当缓冲区达到一定大小时，处理并发送数据
                if (this.audioData.size >= 4096) {
                    this.processAndSendAudio();
                }
            }
        }
        
        // 返回true以保持处理器活跃
        return true;
    }
    
    processAndSendAudio() {
        try {
            // 压缩和重采样音频数据
            const compressedData = this.compressAudio();
            
            // 编码为PCM格式
            const pcmBlob = this.encodePCM(compressedData);
            
            // 发送到主线程
            this.port.postMessage({
                type: 'audioData',
                data: pcmBlob
            });
            
            // 清空缓冲区
            this.clearBuffer();
            
        } catch (error) {
            this.port.postMessage({
                type: 'error',
                error: error.message
            });
        }
    }
    
    compressAudio() {
        // 合并所有缓冲区数据
        const data = new Float32Array(this.audioData.size);
        let offset = 0;
        
        for (let i = 0; i < this.audioData.buffer.length; i++) {
            data.set(this.audioData.buffer[i], offset);
            offset += this.audioData.buffer[i].length;
        }
        
        // 重采样：从48kHz降到16kHz
        const compression = parseInt(this.audioData.inputSampleRate / this.audioData.outputSampleRate);
        const length = data.length / compression;
        const result = new Float32Array(length);
        
        let index = 0;
        let j = 0;
        while (index < length) {
            result[index] = data[j];
            j += compression;
            index++;
        }
        
        return result;
    }
    
    encodePCM(bytes) {
        const sampleBits = Math.min(this.audioData.inputSampleBits, this.audioData.outputSampleBits);
        const dataLength = bytes.length * (sampleBits / 8);
        const buffer = new ArrayBuffer(dataLength);
        const data = new DataView(buffer);
        
        let offset = 0;
        for (let i = 0; i < bytes.length; i++, offset += 2) {
            const s = Math.max(-1, Math.min(1, bytes[i]));
            data.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
        }
        
        return buffer;
    }
    
    clearBuffer() {
        this.audioData.buffer = [];
        this.audioData.size = 0;
    }
}

// 注册处理器
registerProcessor('audio-recorder-processor', AudioRecorderProcessor);
