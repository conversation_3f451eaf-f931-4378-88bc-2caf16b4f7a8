---
trigger: always_on
---

# Always respond in 中文
# 编码风格应该尽量精简，不要冗余。并且需要在关键位置对代码进行注释即可。不需要在完成任务时编写总结报告。
# AI会议记录APP项目架构
- AI会议记录APP项目采用三层架构：Flutter客户端（无头主机+H5单页应用）、Python FastAPI后端、MySQL数据库，集成阿里云实时会议服务进行语音转录和AI摘要。
- Flutter仅作WebView容器托管H5；配置信息存储在web/.env文件。
# 项目根目录中的app为flutter客户端，并在assets中包含了完整的h5代码。而根目录下的web文件夹则为后端fastapi网站相关代码。
# 本项目在Windows环境中进行开发。需要通过conda activate temp进入temp虚拟环境，再使用python启动FastAPI服务端。