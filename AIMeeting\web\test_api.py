#!/usr/bin/env python3
"""
API测试脚本
"""

import httpx
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
AUTH_TOKEN = "your_service_auth_token"  # 替换为实际的认证令牌

headers = {
    "Authorization": AUTH_TOKEN,
    "Content-Type": "application/json"
}

async def test_api():
    async with httpx.AsyncClient() as client:
        print("🚀 开始测试AI会议记录API...")
        
        # 1. 测试创建会议
        print("\n1. 测试创建会议...")
        try:
            response = await client.post(f"{BASE_URL}/api/v1/meetings", headers=headers)
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get("taskId")
                print(f"✅ 会议创建成功，任务ID: {task_id}")
            else:
                print("❌ 会议创建失败")
                return
        except Exception as e:
            print(f"❌ 创建会议时出错: {e}")
            return
        
        # 2. 测试获取会议列表
        print("\n2. 测试获取会议列表...")
        try:
            response = await client.get(f"{BASE_URL}/api/v1/meetings", headers=headers)
            print(f"状态码: {response.status_code}")
            data = response.json()
            print(f"会议数量: {len(data.get('data', []))}")
            print("✅ 会议列表获取成功")
        except Exception as e:
            print(f"❌ 获取会议列表时出错: {e}")
        
        # 3. 测试获取会议详情
        if task_id:
            print(f"\n3. 测试获取会议详情 (任务ID: {task_id})...")
            try:
                response = await client.get(f"{BASE_URL}/api/v1/meetings/{task_id}", headers=headers)
                print(f"状态码: {response.status_code}")
                data = response.json()
                print(f"任务状态: {data.get('taskStatus')}")
                print("✅ 会议详情获取成功")
            except Exception as e:
                print(f"❌ 获取会议详情时出错: {e}")
        
        # 4. 测试结束会议
        if task_id:
            print(f"\n4. 测试结束会议 (任务ID: {task_id})...")
            try:
                response = await client.post(f"{BASE_URL}/api/v1/meetings/{task_id}/stop", headers=headers)
                print(f"状态码: {response.status_code}")
                print(f"响应: {response.json()}")
                print("✅ 会议结束成功")
            except Exception as e:
                print(f"❌ 结束会议时出错: {e}")
        
        print("\n🎉 API测试完成！")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_api())
