start_realtime_meeting    # 创建实时会议任务

req==========
{'AppKey': 'FpbaI8lbsnDHVJ2g', 'Input': {'Format': 'pcm', 'SampleRate': 16000, 'SourceLanguage': 'cn', 'TaskKey': 'task20250724212520', 'ProgressiveCallbacksEnabled': False}, 'Parameters': {'Transcription': {'DiarizationEnabled': True, 'Diarization': {'SpeakerCount': 2}}, 'TranslationEnabled': False, 'AutoChaptersEnabled': False, 'MeetingAssistanceEnabled': True, 'MeetingAssistance': {'Types': ['Actions', 'KeyInformation']}, 'SummarizationEnabled': True, 'Summarization': {'Types': ['Paragraph', 'Conversational']}, 'PptExtractionEnabled': False, 'TextPolishEnabled': False, 'CustomPromptEnabled': False}}

resp==========
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "NEW",
        "MeetingJoinUrl": "wss://tingwu-realtime-cn-beijing.aliyuncs.com/api/ws/v1?mc=3fVfi4-7D8FyKuathCPZEZBBfJIVVVh6wTK8hHpokUaZgxGGln8aeZu09-9iRow-BleBisincglIcKCZte4cTRVAiO_rtlqoxa9qDDufCXngWaYk2IfgFxg2uMosOhdV"
    },
    "Message": "success",
    "RequestId": "A74DAB9B-4049-571D-95CA-D5C8D43AAD2F"
}



end_realtime_meeting   # 结束实施会议任务

req==========
{'AppKey': 'FpbaI8lbsnDHVJ2g', 'Input': {'TaskId': 'b047e09c9dfe4029991f3cdaef521048'}}

resp==========
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "ONGOING"
    },
    "Message": "success",
    "RequestId": "BA7403F0-E6B4-5297-9D70-5406FFD0A762"
}



get_realtime_meeting.py   # 获取会议任务结果

req==========




resp==========
任务还在运行中====
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "ONGOING",
        "OutputMp3Path": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_20250724212525.mp3?Expires=1755955693&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=tNXw%2BZCFiEX%2BeeRlPqZ5byQlBuw%3D"
    },
    "Message": "success",
    "RequestId": "0DD9DF33-714C-530A-9128-56A6DDBB1D15"
}
任务已完成====
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "COMPLETED",
        "OutputMp3Path": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_20250724212525.mp3?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=gALxsF8k6PC2PyikR2BfrajgQYc%3D",
        "Result": {
            "MeetingAssistance": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_MeetingAssistance_20250724212821.json?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=Ihz%2FOxugdSHbVKVyTFxzmnD70sM%3D",
            "Transcription": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_Transcription_20250724212815.json?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=8a%2BrYoAC5QU0ARwXyvovkzPpEuo%3D",
            "Summarization": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_Summarization_20250724212826.json?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=UcMFP4GZUhTnF1wfn6xTWnctgxY%3D"
        }
    },
    "Message": "success",
    "RequestId": "107D0DF8-B665-59DD-99B7-554B7B563C55"
}


MeetingAssistance====    # 智能会议纪要信息
{"TaskId":"b047e09c9dfe4029991f3cdaef521048","MeetingAssistance":{"Keywords":["吃饭","河豚"],"Classifications":{"Interview":0.24274713,"Lecture":0.62068796,"Meeting":0.1365648}}}

Transcription====    # 语音转录结果
{"TaskId":"b047e09c9dfe4029991f3cdaef521048","Transcription":{"AudioInfo":{"Duration":71786},"Paragraphs":[{"ParagraphId":"1753363696876500000","SpeakerId":"1","Words":[{"Id":1,"SentenceId":1,"Start":1230,"End":1704,"Text":"今天"},{"Id":2,"SentenceId":1,"Start":1704,"End":1941,"Text":"你"},{"Id":3,"SentenceId":1,"Start":1941,"End":2415,"Text":"吃饭"},{"Id":4,"SentenceId":1,"Start":2415,"End":2652,"Text":"了"},{"Id":5,"SentenceId":1,"Start":2652,"End":2889,"Text":"吗？"},{"Id":6,"SentenceId":1,"Start":2890,"End":2950,"Text":"我。"}]},{"ParagraphId":"1753363704236500000","SpeakerId":"2","Words":[{"Id":7,"SentenceId":2,"Start":8590,"End":9242,"Text":"不想"},{"Id":8,"SentenceId":2,"Start":9242,"End":9570,"Text":"吃。"}]},{"ParagraphId":"1753363709856500000","SpeakerId":"1","Words":[{"Id":9,"SentenceId":3,"Start":14210,"End":14843,"Text":"为什么"},{"Id":10,"SentenceId":3,"Start":14843,"End":15265,"Text":"不想"},{"Id":11,"SentenceId":3,"Start":15265,"End":16050,"Text":"吃？"}]},{"ParagraphId":"1753363717586500000","SpeakerId":"2","Words":[{"Id":12,"SentenceId":4,"Start":21940,"End":22500,"Text":"肚子"},{"Id":13,"SentenceId":4,"Start":22500,"End":22780,"Text":"不"},{"Id":14,"SentenceId":4,"Start":22780,"End":23060,"Text":"饿。"}]},{"ParagraphId":"1753363722996500000","SpeakerId":"1","Words":[{"Id":15,"SentenceId":5,"Start":27350,"End":27568,"Text":"我"},{"Id":16,"SentenceId":5,"Start":27568,"End":27786,"Text":"看"},{"Id":17,"SentenceId":5,"Start":27786,"End":28004,"Text":"你"},{"Id":18,"SentenceId":5,"Start":28004,"End":28440,"Text":"肚子"},{"Id":19,"SentenceId":5,"Start":28440,"End":28658,"Text":"都"},{"Id":20,"SentenceId":5,"Start":28658,"End":28876,"Text":"晕"},{"Id":21,"SentenceId":5,"Start":28876,"End":29820,"Text":"了。"}]},{"ParagraphId":"1753363732866500000","SpeakerId":"2","Words":[{"Id":22,"SentenceId":6,"Start":37220,"End":37764,"Text":"因为"},{"Id":23,"SentenceId":6,"Start":37764,"End":38308,"Text":"生气"},{"Id":24,"SentenceId":6,"Start":38308,"End":38580,"Text":"了。"}]},{"ParagraphId":"1753363739296500000","SpeakerId":"1","Words":[{"Id":25,"SentenceId":7,"Start":43650,"End":44280,"Text":"生气"},{"Id":26,"SentenceId":7,"Start":44280,"End":44910,"Text":"的话，"},{"Id":27,"SentenceId":7,"Start":44910,"End":45418,"Text":"肚子"},{"Id":28,"SentenceId":7,"Start":45418,"End":45926,"Text":"不是"},{"Id":29,"SentenceId":7,"Start":45926,"End":46434,"Text":"应该"},{"Id":30,"SentenceId":7,"Start":46434,"End":46942,"Text":"鼓起"},{"Id":31,"SentenceId":7,"Start":46942,"End":47196,"Text":"来"},{"Id":32,"SentenceId":7,"Start":47196,"End":47450,"Text":"吗？"}]},{"ParagraphId":"1753363748616500000","SpeakerId":"2","Words":[{"Id":33,"SentenceId":8,"Start":52970,"End":53205,"Text":"你"},{"Id":34,"SentenceId":8,"Start":53205,"End":53675,"Text":"以为"},{"Id":35,"SentenceId":8,"Start":53675,"End":53910,"Text":"我"},{"Id":36,"SentenceId":8,"Start":53910,"End":54145,"Text":"是"},{"Id":37,"SentenceId":8,"Start":54145,"End":54615,"Text":"河豚"},{"Id":38,"SentenceId":8,"Start":54615,"End":54850,"Text":"吗？"}]},{"ParagraphId":"1753363758126500000","SpeakerId":"1","Words":[{"Id":39,"SentenceId":9,"Start":62480,"End":62705,"Text":"我"},{"Id":40,"SentenceId":9,"Start":62705,"End":62930,"Text":"要"},{"Id":41,"SentenceId":9,"Start":62930,"End":64000,"Text":"肚子。"}]}],"AudioSegments":[[1230,2950],[8590,9570],[14210,16050],[21940,23060],[27350,29820],[37220,38580],[43650,47450],[52970,54850],[62480,64000]]}}

Summarization====    # 会议摘要信息
{"TaskId":"b047e09c9dfe4029991f3cdaef521048","Summarization":{"ParagraphSummary":"在一段对话中，两人讨论了是否吃饭的问题。其中一人表示不饿，不愿进食，而另一方敏锐地观察到其可能是因为生气。为了幽默地表达生气时身体的反应，将生气比喻成肚子应该像河豚一样鼓起来，暗示生气并不一定增加食欲。对话重复两次，可能强调了这种情绪与食欲关系的循环或重要性。","ParagraphTitle":"生气与食欲的幽默探讨","ConversationalSummary":[{"SpeakerId":"1","SpeakerName":"发言人1","Summary":"首先关心地询问对方是否已经用餐，当得知对方并无进食的意愿后，他以一种轻松幽默的口吻表达了自己的关切。他提到，当人生气的时候，肚子其实是最无辜的，它会因为主人的情绪而受到牵连，比如变得紧张或不舒服。他以这种幽默的方式提醒对方，即使在情绪不佳的时候，也应该照顾好自己的身体，特别是要善待自己的肚子。最后，他也表达了自己需要好好照顾肚子的愿望，暗示了保持身体健康和情绪稳定的重要性。在整个对话中，他不仅体现了对对方身体健康的关心，也通过幽默的方式传达了积极的生活态度和自我关怀的理念。"},{"SpeakerId":"2","SpeakerName":"发言人2","Summary":"他以一种幽默而又略带生气的口吻表达了自己并不饿，也不想吃东西。他/她用“你以为我是河豚吗？”这个问题，巧妙地传达了自己并不希望被激怒或者被误解的心情。他的核心观点在于，他/她因为生气而没有食欲，明确拒绝了进食的提议，同时也希望他人能够理解和尊重自己的情绪状态。通过这种幽默的表达方式，他既维护了自己的情绪边界，又避免了与他人产生不必要的冲突。"}]}}