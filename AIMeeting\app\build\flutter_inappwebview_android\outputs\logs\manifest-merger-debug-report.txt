-- Merging decision tree log ---
manifest
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:2:1-43:12
	package
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:3:5-63
		INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:2:11-69
application
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:4:5-35:19
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:5:9-9:140
	android:exported
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:7:13-37
	android:configChanges
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:9:13-137
	android:theme
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:6:13-44
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:8:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:10:9-13:123
	android:exported
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:12:13-37
	android:theme
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:11:13-52
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:13:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:14:9-17:117
	android:exported
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:15:13-52
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:17:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:18:9-22:50
	android:launchMode
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:22:13-48
	android:exported
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:20:13-37
	android:theme
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:19:13-52
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:21:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:23:9-27:50
	android:launchMode
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:27:13-48
	android:exported
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:24:13-52
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:26:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:28:9-31:40
	android:enabled
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:30:13-35
	android:exported
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:31:13-37
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:29:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:32:9-34:36
	android:value
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:34:13-33
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:33:13-61
queries
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:37:5-42:15
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:38:9-41:18
action#android.support.customtabs.action.CustomTabsService
ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:39:13-40:73
	android:name
		ADDED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:39:21-104
uses-sdk
INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml
INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml
