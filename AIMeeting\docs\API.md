# **阿里云实时会议服务 API 文档 (通过SDK方法封装)**

## 1. 概述

本服务提供实时的语音转文字、会议纪要、摘要等功能。开发者通过调用本API，可以创建一个实时会议任务，通过WebSocket推送音频流，并在会议结束后获取结构化的处理结果。

**核心流程：**
1.  调用 `start_realtime_meeting` 创建一个新任务，获取 `TaskId` 和 WebSocket 连接地址 `MeetingJoinUrl`。
2.  客户端使用 `MeetingJoinUrl` 建立 WebSocket 连接，并持续推送实时音频流。
3.  音频推送结束后，客户端关闭 WebSocket 连接，并调用 `end_realtime_meeting` 通知服务端音频已发送完毕。
4.  客户端轮询调用 `get_realtime_meeting` 接口，查询任务处理状态，直到任务完成 (`TaskStatus: "COMPLETED"`)。
5.  从 `get_realtime_meeting` 的结果中获取转录、摘要等数据的下载地址，并下载最终结果。

## 2. 通用说明

*   **请求域名 (Base URL)**: `https://tingwu-realtime.aliyuncs.com` (此为示例，请使用实际提供的域名)
*   **认证方式**: 所有请求都需要在请求体中包含 `AppKey` 参数。
*   **内容类型**: `Content-Type: application/json`

---

## 3. API 接口详情

### 3.1 创建实时会议任务 (start_realtime_meeting)

此接口用于初始化一个实时会议任务，并返回用于推送音频流的 WebSocket 地址。

*   **接口名**: `start_realtime_meeting`
*   **HTTP 方法**: `POST`

#### 请求参数 (Request)

| 参数路径 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `AppKey` | String | 是 | 您的应用密钥。 |
| `Input` | Object | 是 | 输入配置对象。 |
| `Input.Format` | String | 是 | 音频格式，示例: `"pcm"`。 |
| `Input.SampleRate` | Integer | 是 | 音频采样率，示例: `16000`。 |
| `Input.SourceLanguage` | String | 是 | 音频源语言，示例: `"cn"` (中文)。 |
| `Input.TaskKey` | String | 否 | 客户端自定义的任务标识，方便追踪。 |
| `Input.ProgressiveCallbacksEnabled` | Boolean | 否 | 是否启用渐进式回调。默认为 `False`。 |
| `Parameters` | Object | 是 | 功能参数配置对象。 |
| `Parameters.Transcription.DiarizationEnabled`| Boolean | 是 | 是否开启说话人分离。 |
| `Parameters.Transcription.Diarization.SpeakerCount`| Integer| 否 | （若开启分离）预设的说话人数量。 |
| `Parameters.TranslationEnabled`| Boolean | 是 | 是否开启翻译功能。 |
| `Parameters.AutoChaptersEnabled` | Boolean | 是 | 是否开启自动章节功能。 |
| `Parameters.MeetingAssistanceEnabled` | Boolean | 是 | 是否开启智能会议纪要。 |
| `Parameters.MeetingAssistance.Types` | Array | 否 | （若开启纪要）需要生成的纪要类型，如`"Actions"`, `"KeyInformation"`。 |
| `Parameters.SummarizationEnabled` | Boolean | 是 | 是否开启摘要功能。 |
| `Parameters.Summarization.Types` | Array | 否 | （若开启摘要）需要生成的摘要类型，如`"Paragraph"` (全文摘要), `"Conversational"` (分角色摘要)。 |
| `Parameters.PptExtractionEnabled`| Boolean | 是 | 是否开启PPT提取功能。 |
| `Parameters.TextPolishEnabled`| Boolean | 是 | 是否开启文本润色功能。 |
| `Parameters.CustomPromptEnabled`| Boolean | 是 | 是否开启自定义提示词功能。 |

#### 请求示例

```json
{
    "AppKey": "FpbaI8lbsnDHVJ2g",
    "Input": {
        "Format": "pcm",
        "SampleRate": 16000,
        "SourceLanguage": "cn",
        "TaskKey": "task20250724212520",
        "ProgressiveCallbacksEnabled": false
    },
    "Parameters": {
        "Transcription": {
            "DiarizationEnabled": true,
            "Diarization": {
                "SpeakerCount": 2
            }
        },
        "TranslationEnabled": false,
        "AutoChaptersEnabled": false,
        "MeetingAssistanceEnabled": true,
        "MeetingAssistance": {
            "Types": [
                "Actions",
                "KeyInformation"
            ]
        },
        "SummarizationEnabled": true,
        "Summarization": {
            "Types": [
                "Paragraph",
                "Conversational"
            ]
        },
        "PptExtractionEnabled": false,
        "TextPolishEnabled": false,
        "CustomPromptEnabled": false
    }
}
```

#### 响应 (Response)

| 参数路径 | 类型 | 描述 |
| :--- | :--- | :--- |
| `Code` | String | 结果码，`"0"` 表示成功。 |
| `Message` | String | 结果描述，如 `"success"`。 |
| `RequestId` | String | 本次请求的唯一ID。 |
| `Data` | Object | 返回的核心数据对象。 |
| `Data.TaskId` | String | 系统生成的唯一任务ID，后续操作凭此ID进行。 |
| `Data.TaskKey` | String | 客户端传入的任务标识。 |
| `Data.TaskStatus`| String | 任务当前状态，此处为 `"NEW"`。 |
| `Data.MeetingJoinUrl`| String | WebSocket 连接地址，用于推送音频流。 |

#### 响应示例

```json
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "NEW",
        "MeetingJoinUrl": "wss://tingwu-realtime-cn-beijing.aliyuncs.com/api/ws/v1?mc=3fVfi4-7D8FyKuathCPZEZBBfJIVVVh6wTK8hHpokUaZgxGGln8aeZu09-9iRow-BleBisincglIcKCZte4cTRVAiO_rtlqoxa9qDDufCXngWaYk2IfgFxg2uMosOhdV"
    },
    "Message": "success",
    "RequestId": "A74DAB9B-4049-571D-95CA-D5C8D43AAD2F"
}
```

---

### 3.2 结束实时会议任务 (end_realtime_meeting)

此接口用于通知服务端，指定任务的音频流已全部推送完毕，可以开始进行后续处理。

*   **接口名**: `end_realtime_meeting`
*   **HTTP 方法**: `POST`

#### 请求参数 (Request)

| 参数路径 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `AppKey` | String | 是 | 您的应用密钥。 |
| `Input` | Object | 是 | 输入配置对象。 |
| `Input.TaskId` | String | 是 | 需要结束的任务ID，由 `start_realtime_meeting` 接口返回。 |

#### 请求示例

```json
{
    "AppKey": "FpbaI8lbsnDHVJ2g",
    "Input": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048"
    }
}
```

#### 响应 (Response)

响应结构与 `start_realtime_meeting` 类似，但 `TaskStatus` 会变为 `"ONGOING"`，表示任务已进入处理队列。

#### 响应示例

```json
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "ONGOING"
    },
    "Message": "success",
    "RequestId": "BA7403F0-E6B4-5297-9D70-5406FFD0A762"
}
```

---

### 3.3 获取会议任务结果 (get_realtime_meeting)

此接口用于轮询查询任务的处理状态和最终结果。

*   **接口名**: `get_realtime_meeting`
*   **HTTP 方法**: `POST`

#### 请求参数 (Request)

| 参数路径 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `AppKey` | String | 是 | 您的应用密钥。 |
| `Input` | Object | 是 | 输入配置对象。 |
| `Input.TaskId` | String | 是 | 需要查询的任务ID。 |

#### 请求示例
*该接口请求体为空，查询参数在URL中传递，此处省略请求体示例。*
*(注: 原始文件`req`部分为空，实际请求体结构可能与 `end_realtime_meeting` 类似，包含`AppKey`和`Input.TaskId`)*

#### 响应 (Response)

响应内容根据任务状态而不同。

| 参数路径 | 类型 | 描述 |
| :--- | :--- | :--- |
| `Code` | String | 结果码，`"0"` 表示成功。 |
| `Message` | String | 结果描述。 |
| `RequestId` | String | 本次请求的唯一ID。 |
| `Data.TaskId` | String | 任务ID。 |
| `Data.TaskStatus`| String | 任务状态，`"ONGOING"` (处理中) 或 `"COMPLETED"` (已完成)。 |
| `Data.OutputMp3Path` | String | (可选) 会议录音的MP3下载地址。 |
| `Data.Result` | Object | **仅在任务完成后出现**，包含各项结果的下载地址。 |
| `Data.Result.MeetingAssistance` | String | 智能会议纪要结果JSON文件的下载地址。 |
| `Data.Result.Transcription` | String | 语音转录结果JSON文件的下载地址。 |
| `Data.Result.Summarization` | String | 会议摘要结果JSON文件的下载地址。 |


#### 响应示例 1: 任务处理中

```json
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "ONGOING",
        "OutputMp3Path": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_20250724212525.mp3?Expires=1755955693&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=tNXw%2BZCFiEX%2BeeRlPqZ5byQlBuw%3D"
    },
    "Message": "success",
    "RequestId": "0DD9DF33-714C-530A-9128-56A6DDBB1D15"
}
```

#### 响应示例 2: 任务已完成

```json
{
    "Code": "0",
    "Data": {
        "TaskId": "b047e09c9dfe4029991f3cdaef521048",
        "TaskKey": "task20250724212520",
        "TaskStatus": "COMPLETED",
        "OutputMp3Path": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_20250724212525.mp3?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=gALxsF8k6PC2PyikR2BfrajgQYc%3D",
        "Result": {
            "MeetingAssistance": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_MeetingAssistance_20250724212821.json?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=Ihz%2FOxugdSHbVKVyTFxzmnD70sM%3D",
            "Transcription": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_Transcription_20250724212815.json?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=8a%2BrYoAC5QU0ARwXyvovkzPpEuo%3D",
            "Summarization": "https://prod-tingwu-paas-common-beijing.oss-cn-beijing.aliyuncs.com/tingwu/output/1527925811592927/b047e09c9dfe4029991f3cdaef521048/b047e09c9dfe4029991f3cdaef521048_Summarization_20250724212826.json?Expires=1755955976&OSSAccessKeyId=LTAI5tMzZ1D4o1drkJN1TfCr&Signature=UcMFP4GZUhTnF1wfn6xTWnctgxY%3D"
        }
    },
    "Message": "success",
    "RequestId": "107D0DF8-B665-59DD-99B7-554B7B563C55"
}
```

---

## 4. 结果数据结构

以下是 `get_realtime_meeting` 接口返回的下载链接中的JSON文件内容结构。

### 4.1 智能会议纪要 (MeetingAssistance)

描述会议的核心关键词和分类。

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `TaskId` | String | 任务ID。 |
| `MeetingAssistance` | Object | 纪要内容。 |
| `MeetingAssistance.Keywords` | Array | 关键词列表。 |
| `MeetingAssistance.Classifications` | Object | 会议分类及置信度得分。 |

**示例:**
```json
{
    "TaskId": "b047e09c9dfe4029991f3cdaef521048",
    "MeetingAssistance": {
        "Keywords": [
            "吃饭",
            "河豚"
        ],
        "Classifications": {
            "Interview": 0.24274713,
            "Lecture": 0.62068796,
            "Meeting": 0.1365648
        }
    }
}
```

### 4.2 语音转录结果 (Transcription)

包含详细的语音转文字结果、时间戳和说话人信息。

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `TaskId` | String | 任务ID。 |
| `Transcription.AudioInfo.Duration` | Integer | 音频总时长（毫秒）。 |
| `Transcription.Paragraphs` | Array | 段落数组，每个对象代表一个说话人连续说的一段话。 |
| `Paragraphs[].ParagraphId` | String | 段落唯一ID。 |
| `Paragraphs[].SpeakerId` | String | 说话人ID。 |
| `Paragraphs[].Words` | Array | 词语信息数组。 |
| `Words[].Id`| Integer| 词语在整个对话中的唯一ID。 |
| `Words[].SentenceId`| Integer| 词语所属的句子ID。 |
| `Words[].Text` | String | 识别出的词语。 |
| `Words[].Start` | Integer | 词语在音频中的开始时间（毫秒）。 |
| `Words[].End` | Integer | 词语在音频中的结束时间（毫秒）。 |
| `Transcription.AudioSegments` | Array | 音频有效片段的起止时间数组。 |

**示例:**
```json
{
    "TaskId": "b047e09c9dfe4029991f3cdaef521048",
    "Transcription": {
        "AudioInfo": {
            "Duration": 71786
        },
        "Paragraphs": [
            {
                "ParagraphId": "1753363696876500000",
                "SpeakerId": "1",
                "Words": [
                    {"Id": 1,"SentenceId": 1,"Start": 1230,"End": 1704,"Text": "今天"},
                    {"Id": 2,"SentenceId": 1,"Start": 1704,"End": 1941,"Text": "你"},
                    {"Id": 3,"SentenceId": 1,"Start": 1941,"End": 2415,"Text": "吃饭"},
                    {"Id": 4,"SentenceId": 1,"Start": 2415,"End": 2652,"Text": "了"},
                    {"Id": 5,"SentenceId": 1,"Start": 2652,"End": 2889,"Text": "吗？"},
                    {"Id": 6,"SentenceId": 1,"Start": 2890,"End": 2950,"Text": "我。"}
                ]
            },
            {
                "ParagraphId": "1753363704236500000",
                "SpeakerId": "2",
                "Words": [
                    {"Id": 7,"SentenceId": 2,"Start": 8590,"End": 9242,"Text": "不想"},
                    {"Id": 8,"SentenceId": 2,"Start": 9242,"End": 9570,"Text": "吃。"}
                ]
            }
        ],
        "AudioSegments": [
            [1230, 2950],
            [8590, 9570],
            [14210, 16050],
            [21940, 23060],
            [27350, 29820],
            [37220, 38580],
            [43650, 47450],
            [52970, 54850],
            [62480, 64000]
        ]
    }
}
```
*(注: 为简洁起见，`Paragraphs` 数组示例仅展示了部分内容)*

### 4.3 会议摘要信息 (Summarization)

包含全文摘要和分角色摘要。

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `TaskId` | String | 任务ID。 |
| `Summarization.ParagraphTitle` | String | AI生成的全文摘要标题。 |
| `Summarization.ParagraphSummary` | String | AI生成的全文摘要内容。 |
| `Summarization.ConversationalSummary` | Array | 分角色摘要数组。 |
| `ConversationalSummary[].SpeakerId` | String | 说话人ID。 |
| `ConversationalSummary[].SpeakerName` | String | 说话人名称（如 `发言人1`）。 |
| `ConversationalSummary[].Summary`| String | 该说话人的发言摘要。 |

**示例:**
```json
{
    "TaskId": "b047e09c9dfe4029991f3cdaef521048",
    "Summarization": {
        "ParagraphSummary": "在一段对话中，两人讨论了是否吃饭的问题。其中一人表示不饿，不愿进食，而另一方敏锐地观察到其可能是因为生气。为了幽默地表达生气时身体的反应，将生气比喻成肚子应该像河豚一样鼓起来，暗示生气并不一定增加食欲。对话重复两次，可能强调了这种情绪与食欲关系的循环或重要性。",
        "ParagraphTitle": "生气与食欲的幽默探讨",
        "ConversationalSummary": [
            {
                "SpeakerId": "1",
                "SpeakerName": "发言人1",
                "Summary": "首先关心地询问对方是否已经用餐，当得知对方并无进食的意愿后，他以一种轻松幽默的口吻表达了自己的关切。他提到，当人生气的时候，肚子其实是最无辜的，它会因为主人的情绪而受到牵连，比如变得紧张或不舒服。他以这种幽默的方式提醒对方，即使在情绪不佳的时候，也应该照顾好自己的身体，特别是要善待自己的肚子。最后，他也表达了自己需要好好照顾肚子的愿望，暗示了保持身体健康和情绪稳定的重要性。在整个对话中，他不仅体现了对对方身体健康的关心，也通过幽默的方式传达了积极的生活态度和自我关怀的理念。"
            },
            {
                "SpeakerId": "2",
                "SpeakerName": "发言人2",
                "Summary": "他以一种幽默而又略带生气的口吻表达了自己并不饿，也不想吃东西。他/她用“你以为我是河豚吗？”这个问题，巧妙地传达了自己并不希望被激怒或者被误解的心情。他的核心观点在于，他/她因为生气而没有食欲，明确拒绝了进食的提议，同时也希望他人能够理解和尊重自己的情绪状态。通过这种幽默的表达方式，他既维护了自己的情绪边界，又避免了与他人产生不必要的冲突。"
            }
        ]
    }
}
```

## 5. 语音实时转录结果

在H5中通过WSS获取到的是一个标准的MessageEvent类型对象，需要取出data字段值进行处理。data字段其类型为字符串，一个常规的句子结束事件对应的data值为：
```
{"header":{"nls_task_id":"7cd37516d5a0439a92cabe134760e115","task_key":"task20250726160512","namespace":"SpeechTranscriber","name":"SentenceEnd","task_id":"758dcc5245b14b0398f84c7561ee7a54","message_id":"a833d4aeca144c5281b7f19c141cdf77","status_text":"Success.","status":20000000},"payload":{"gender":"","sentence_id":"76994ff4ac774bcebeb12e9360e2e295","audio_extra_info":"","confidence":0.0,"words":[{"startTime":125744,"text":"大家","endTime":126244},{"startTime":126244,"text":"好","endTime":126494,"punc":"，"},{"startTime":126494,"text":"我","endTime":126724},{"startTime":126724,"text":"是","endTime":126954},{"startTime":126954,"text":"王","endTime":127184},{"startTime":127184,"text":"德威","endTime":127644,"punc":"。"}],"index":0,"begin_time":125334,"gender_score":0.0,"result":"大家好，我是王德威。","time":127644,"fixed_result":"","unfixed_result":"","status":0,"stash_result":{"currentTime":127644,"words":[],"sentenceId":1,"index":1,"unfixedText":"","beginTime":127644,"text":"","fixedText":""}}}
```
在将字符串反序列化为对象后，可以根据header.name判断具体事件类型，再进行对应处理。
例如对于句子结束事件，则可以取出payload.result作为语音识别结果，显示到APP界面中。