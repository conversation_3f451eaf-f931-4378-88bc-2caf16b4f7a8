from pydantic import BaseModel
from typing import List, Optional, Any
from datetime import datetime
from .models import TaskStatusEnum

# Pydantic模型用于API的数据验证和序列化

# 子表的Schema
class Transcription(BaseModel):
    audio_duration_ms: Optional[int]
    paragraphs: Any
    audio_segments: Optional[Any]

    class Config:
        from_attributes = True # 从ORM模型属性自动映射

class Summarization(BaseModel):
    paragraph_title: Optional[str]
    paragraph_summary: Optional[str]
    conversational_summary: Optional[Any]

    class Config:
        from_attributes = True

class MeetingAssistance(BaseModel):
    keywords: Optional[Any]
    classifications: Optional[Any]

    class Config:
        from_attributes = True

# 主任务的Schema
class TaskBase(BaseModel):
    task_id: str
    task_key: Optional[str] = None
    task_status: TaskStatusEnum

# 用于任务列表的Schema
class TaskListItem(TaskBase):
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 用于获取单个任务详情的Schema，包含所有关联数据
class TaskDetail(TaskListItem):
    meeting_join_url: Optional[str] = None
    output_mp3_path: Optional[str] = None
    transcription: Optional[Transcription] = None
    summarization: Optional[Summarization] = None
    assistance: Optional[MeetingAssistance] = None

# 用于分页返回任务列表的Schema
class PaginatedTaskList(BaseModel):
    total: int
    page: int
    size: int
    data: List[TaskListItem]