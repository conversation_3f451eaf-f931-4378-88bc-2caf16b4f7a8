  Bundle android.app.Activity  
WindowManager android.app.Activity  android android.app.Activity  onCreate android.app.Activity  Bundle android.content.Context  
WindowManager android.content.Context  android android.content.Context  onCreate android.content.Context  Bundle android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  android android.content.ContextWrapper  onCreate android.content.ContextWrapper  Color android.graphics  TRANSPARENT android.graphics.Color  
parseColor android.graphics.Color  Bundle 
android.os  Window android.view  
WindowManager android.view  Bundle  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  addFlags android.view.Window  getNAVIGATIONBarColor android.view.Window  getNavigationBarColor android.view.Window  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  navigationBarColor android.view.Window  setNavigationBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  LayoutParams android.view.WindowManager  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  MainActivity com.jnz.ai_meeting_app  
WindowManager com.jnz.ai_meeting_app  android com.jnz.ai_meeting_app  Bundle #com.jnz.ai_meeting_app.MainActivity  
WindowManager #com.jnz.ai_meeting_app.MainActivity  android #com.jnz.ai_meeting_app.MainActivity  
getANDROID #com.jnz.ai_meeting_app.MainActivity  
getAndroid #com.jnz.ai_meeting_app.MainActivity  	getWINDOW #com.jnz.ai_meeting_app.MainActivity  	getWindow #com.jnz.ai_meeting_app.MainActivity  	setWindow #com.jnz.ai_meeting_app.MainActivity  window #com.jnz.ai_meeting_app.MainActivity  FlutterActivity io.flutter.embedding.android  Bundle ,io.flutter.embedding.android.FlutterActivity  
WindowManager ,io.flutter.embedding.android.FlutterActivity  android ,io.flutter.embedding.android.FlutterActivity  onCreate ,io.flutter.embedding.android.FlutterActivity  
WindowManager 	java.lang  android 	java.lang  Int kotlin  String kotlin  
WindowManager kotlin  android kotlin  
WindowManager kotlin.annotation  android kotlin.annotation  
WindowManager kotlin.collections  android kotlin.collections  
WindowManager kotlin.comparisons  android kotlin.comparisons  
WindowManager 	kotlin.io  android 	kotlin.io  
WindowManager 
kotlin.jvm  android 
kotlin.jvm  
WindowManager 
kotlin.ranges  android 
kotlin.ranges  
WindowManager kotlin.sequences  android kotlin.sequences  
WindowManager kotlin.text  android kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  