#!/usr/bin/env python
# coding=utf-8


import json
import datetime
from .config import settings
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from aliyunsdkcore.auth.credentials import AccessKeyCredential

credentials = AccessKeyCredential(
    settings.ALIYUN_ACCESS_KEY_ID, settings.ALIYUN_ACCESS_KEY_SECRET)
client = AcsClient(region_id='cn-beijing', credential=credentials)


def create_start_request(domain, version, protocolType, method, uri):
    request = CommonRequest()
    request.set_accept_format('json')
    request.set_domain(domain)
    request.set_version(version)
    request.set_protocol_type(protocolType)
    request.set_method(method)
    request.set_uri_pattern(uri)
    request.add_header('Content-Type', 'application/json')
    return request


def init_start_params():
    body = dict()

    body['AppKey'] = settings.ALIYUN_APP_KEY

    # 基本请求参数
    input = dict()

    # 输入语音流格式和采样率和以下参数设置保持一致
    input['Format'] = 'pcm'
    input['SampleRate'] = 16000
    input['SourceLanguage'] = 'cn'
    input['TaskKey'] = 'task' + \
        datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    input['ProgressiveCallbacksEnabled'] = False
    body['Input'] = input

    # AI相关参数，按需设置即可
    parameters = dict()

    # 音视频转换相关
    transcoding = dict()
    # 将原音视频文件转成mp3文件，用以后续浏览器播放
    # transcoding['TargetAudioFormat'] = 'mp3'
    # transcoding['SpectrumEnabled'] = False
    # parameters['Transcoding'] = transcoding

    # 语音识别控制相关
    transcription = dict()
    # 角色分离
    transcription['DiarizationEnabled'] = True
    diarization = dict()
    diarization['SpeakerCount'] = 2
    transcription['Diarization'] = diarization
    parameters['Transcription'] = transcription

    # parameters['IdentityRecognitionEnabled'] = True
    # identity_recognition = {
    #     "SceneIntroduction": "汽车门店线下销售场景",
    #     "IdentityContents": [
    #         {
    #             "Name": "销售",
    #             "Description": "介绍车辆的不同配置、性能与技术、舒适性与便利性等"
    #         },
    #         {
    #             "Name": "客户",
    #             "Description": "对车辆提出疑问，表达使用感受等"
    #         }
    #     ]
    # }
    # parameters['IdentityRecognition'] = identity_recognition

    # 文本翻译控制相关
    parameters['TranslationEnabled'] = False
    # translation = dict()
    # translation['TargetLanguages'] = ['en']  # 假设翻译成英文
    # parameters['Translation'] = translation

    # 章节速览相关，包括： 标题、议程摘要
    parameters['AutoChaptersEnabled'] = False

    # 智能纪要相关，包括： 待办、关键信息(关键词、重点内容、场景识别)
    parameters['MeetingAssistanceEnabled'] = True
    meetingAssistance = dict()
    meetingAssistance['Types'] = ['Actions', 'KeyInformation']
    parameters['MeetingAssistance'] = meetingAssistance

    # 摘要控制相关，包括： 全文摘要、发言人总结摘要、问答摘要(问答回顾)
    parameters['SummarizationEnabled'] = True
    summarization = dict()
    summarization['Types'] = ['Paragraph',  'Conversational']
    parameters['Summarization'] = summarization

    # ppt抽取和ppt总结
    parameters['PptExtractionEnabled'] = False

    # 口语书面化
    parameters['TextPolishEnabled'] = False

    # 自定义Prompt处理
    parameters['CustomPromptEnabled'] = False
    # customPrompt = dict()
    # customPrompt['Contents'] = [
    #     {
    #         "Name": "split-summary-demo",
    #         "Prompt": "请帮我将下面的对话进行总结，根据发言人来总结:\n {Transcription}",
    #         "Model": "tingwu-turbo",
    #         "TransType": "chat"
    #     },
    #     {
    #         "Name": "inspection-demo",
    #         "Prompt": "请帮我检查对话内容是否存在不文明用语，如果有请回复'有'，如果没有请回复'无'，对话内容如下:\n {Transcription}",
    #         "Model": "tingwu-turbo",
    #         "TransType": "default"
    #     }
    # ]
    # parameters['CustomPrompt'] = customPrompt

    body['Parameters'] = parameters
    return body


def start_realtime_meeting():
    # 入门配置
    # https://help.aliyun.com/zh/tingwu/getting-started-1

    # https://help.aliyun.com/zh/tingwu/interface-and-implementation#bf41578079kw5

    request = create_start_request(
        settings.ALIYUN_API_BASE_URL, '2023-09-30', 'https', 'PUT', '/openapi/tingwu/v2/tasks')
    request.add_query_param('type', 'realtime')

    body = init_start_params()
    request.set_content(json.dumps(body).encode('utf-8'))
    response = client.do_action_with_exception(request)
    return json.loads(response)


def create_end_request(domain, version, protocolType, method, uri):
    request = CommonRequest()
    request.set_accept_format('json')
    request.set_domain(domain)
    request.set_version(version)
    request.set_protocol_type(protocolType)
    request.set_method(method)
    request.set_uri_pattern(uri)
    request.add_header('Content-Type', 'application/json')
    return request


def init_end_params(task_id):
    body = dict()

    body['AppKey'] = settings.ALIYUN_APP_KEY

    # 基本请求参数
    input = dict()

    # 输入语音流格式和采样率和以下参数设置保持一致
    input['TaskId'] = task_id
    body['Input'] = input

    return body


def end_realtime_meeting(task_id):
    # https://help.aliyun.com/zh/tingwu/interface-and-implementation#0de5549079i0x

    request = create_end_request(
        'tingwu.cn-beijing.aliyuncs.com', '2023-09-30', 'https', 'PUT', '/openapi/tingwu/v2/tasks')
    request.add_query_param('type', 'realtime')
    request.add_query_param('operation', 'stop')

    body = init_end_params(task_id)
    request.set_content(json.dumps(body).encode('utf-8'))
    response = client.do_action_with_exception(request)
    return json.loads(response)


def create_get_request(domain, version, protocolType, method, uri):
    request = CommonRequest()
    request.set_accept_format('json')
    request.set_domain(domain)
    request.set_version(version)
    request.set_protocol_type(protocolType)
    request.set_method(method)
    request.set_uri_pattern(uri)
    request.add_header('Content-Type', 'application/json')
    return request


def get_realtime_meeting(task_id):
    # https://help.aliyun.com/zh/tingwu/get-results

    uri = '/openapi/tingwu/v2/tasks' + '/' + task_id
    request = create_get_request(
        'tingwu.cn-beijing.aliyuncs.com', '2023-09-30', 'https', 'GET', uri)

    response = client.do_action_with_exception(request)
    return json.loads(response)


# resp1 = start_realtime_meeting()
# print("response: \n" + json.dumps(resp1, indent=4, ensure_ascii=False))

# task_id = '66f37b094c9e4e5594a33109cc61ecd5'

# resp2 = end_realtime_meeting(task_id)
# print("response: \n" + json.dumps(resp2, indent=4, ensure_ascii=False))

# resp3 = get_realtime_meeting(task_id)
# print("response: \n" + json.dumps(resp3, indent=4, ensure_ascii=False))
