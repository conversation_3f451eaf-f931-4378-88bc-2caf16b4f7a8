from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    # 从.env文件中读取的配置项

    # MySQL数据库配置
    DATABASE_URL: str

    # 阿里云API配置
    ALIYUN_API_BASE_URL: str
    ALIYUN_ACCESS_KEY_ID: str
    ALIYUN_ACCESS_KEY_SECRET: str
    ALIYUN_APP_KEY: str

    # 本地服务配置
    SVC_AUTH: str   # 这是一个固定的令牌，客户端和后端服务将在Authorization头中使用它
    POLLING_INTERVAL_SECONDS: int   # 轮询器配置：每隔多少秒检查一次任务结果

    # 指定从.env文件加载配置
    model_config = SettingsConfigDict(env_file=".env")

# 创建配置实例
settings = Settings()