version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ai_meeting_mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ai_meeting
      MYSQL_USER: ai_meeting_user
      MYSQL_PASSWORD: ai_meeting_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  # FastAPI应用
  api:
    build: .
    container_name: ai_meeting_api
    environment:
      DATABASE_URL: mysql+pymysql://ai_meeting_user:ai_meeting_pass@mysql:3306/ai_meeting
      ALIYUN_API_BASE_URL: tingwu.cn-beijing.aliyuncs.com
      ALIYUN_ACCESS_KEY_ID: ${ALIYUN_ACCESS_KEY_ID}
      ALIYUN_ACCESS_KEY_SECRET: ${ALIYUN_ACCESS_KEY_SECRET}
      ALIYUN_APP_KEY: ${ALIYUN_APP_KEY}
      SVC_AUTH: ${SVC_AUTH}
      POLLING_INTERVAL_SECONDS: 30
    ports:
      - "8000:8000"
    depends_on:
      - mysql
    restart: unless-stopped
    volumes:
      - .:/app
    command: python run.py

volumes:
  mysql_data:
