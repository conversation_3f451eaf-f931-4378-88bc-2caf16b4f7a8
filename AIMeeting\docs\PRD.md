# 产品需求文档：AI会议记录APP (MVP) - V1.0

## 1. 修订历史

| 版本号 | 修订日期   | 修订人 | 修订内容/主要决策   |
| ------ | ---------- | ------ | ------------------ |
| V1.0   | 2025-07-25 | (AI)   | 初始稿。            |

## 2. 需求概述 (Overview)

### 2.1 项目背景与核心问题

本项目旨在为企业用户开发一款集成了AI能力的会议记录APP。当前，企业用户主要依赖人工进行会议记录，这一过程存在**工作量大、效率低下、容易出错、信息遗漏、以及可能带有记录人主观偏见**等核心痛点。本产品通过AI技术自动化会议转录流程，旨在提供一个客观、精准、高效的会议纪要解决方案。

### 2.2 需求目标与价值

*   **核心目标**: 打造一个MVP（最小可行产品），验证核心的“实时会议转录”与“会后记录查看”流程，用于向潜在客户或投资人进行功能演示。
*   **用户价值**:
    *   将用户从繁重的会议纪要工作中解放出来，专注于会议本身。
    *   提供一份可追溯、客观、完整的会议对话记录。
    *   通过AI自动区分不同发言人，使会议记录的结构更清晰。
    *   **数据保存在服务端**，为未来多设备查看奠定基础。
*   **未来愿景**: 为后续的AI功能扩展（如RAG内容检索、任务自动生成、团队协作等）打下坚实的数据基础和可扩展的后端服务架构。

### 2.3 范围定义 (Scope)

*   **MVP包含的功能 (In-Scope)**:
    1.  **后端服务**: 一个核心的Python FastAPI后端服务，负责业务逻辑和与阿里云交互。
    2.  **核心会议流程**: 一键开始、实时转录、手动结束、查看详情。
    3.  **会议中的实时界面展示**：显示持续时长，并实时展示发言内容。
    4.  **会议历史列表**：从服务端拉取。
    5.  **会议详情页展示**：从服务端拉取基本信息、AI摘要、音频播放、按发言角色区分的完整会议对话文本。
    6.  **核心异常流程处理**：如网络中断等。

*   **MVP不包含的功能 (Out-of-Scope)**:
    1.  用户账户系统（登录、注册、权限等），采用固定API Key认证。
    2.  手动编辑说话人名称或转录文本。
    3.  文本与音频播放时间点联动。
    4.  分享、导出会议纪要。
    5.  复杂的断网重连与本地缓存机制。
    6.  通过RAG进行会议内容检索。
    7.  自动生成任务计划。

## 3. 用户画像与场景 (User Persona & Scenarios)

### 3.1 目标用户 (Target User)

*   **角色**: 企业内的项目经理、团队负责人、产品经理、销售等需要频繁开会并对会议结论负责的职场人士。
*   **痛点**: 被撰写和整理会议纪要占用了大量时间；回顾会议时，因记录不全而无法准确回忆关键决策和讨论细节。

### 3.2 用户场景 (User Scenarios)

项目经理Alex即将与一个重要客户进行项目启动会。他希望将会议的所有讨论细节都准确记录下来。他拿出手机，打开AI会议记录APP。点击“开始会议”后，他在会议列表中看到了这条新记录，随后他将手机放在会议桌中央。会议中，他可以实时看到客户和自己团队成员的发言被准确地转换成文字记录下来。会议结束后，他点击“结束会议”，APP提示“会议纪要正在生成中”。稍后，页面自动跳转进入了详情页，他看到了完整的会议摘要、对话内容和可播放的音频，这让他对接下来的工作充满了信心。

## 4. 功能需求详述 (Functional Requirements)

---

### 4.1 功能模块一：核心会议流程

#### 4.1.1 用户故事 1.1: 进行一场实时转录的会议

*   **用户故事 (User Story)**: **作为** 一名企业用户, **我希望** 能一键开始会议，实时看到会议持续时长以及文字转录内容, **以便于** 我能毫不费力地获得一份客观的会议对话记录。

*   **前置条件 (Pre-conditions)**:
    1.  用户已打开APP并处于首页。
    2.  设备麦克风权限已授予。
    3.  **设备必须连接到互联网。**

*   **后置条件 (Post-conditions)**:
    1.  会议结束后，用户被导航至会议详情页，可以看到刚刚结束的会议信息（可能带有“处理中”的状态标识）。

*   **业务规则与逻辑 (Business Logic)**:
    1.  **开始会议**:
        *   首页中央有一个清晰的“开始会议”按钮。
        *   用户点击“开始会议”。APP调用后端“创建会议”接口。
        *   APP收到后端返回的成功响应（包含`taskId`和`meetingJoinUrl`）后，按钮样式发生变化（例如变为“结束会议”），并进入“会议中”状态。
        *   APP开始通过`meetingJoinUrl`建立WebSocket连接，并同时通过麦克风采集音频流实时推送。
        *   界面开始显示会议时长计时器，从00:00开始计时，整体界面呈现美观的动态效果。
    2.  **会议进行中**:
        *   APP的WebSocket连接会收到实时转录片段，并从`payload.result`中获取到当前句子的转录结果。
        *   界面以“字幕式”效果实时滚动显示转录内容。
        *   每当触发`句子结束`事件时，必须添加一个换行符，以便从新的一行开始显示下一句内容。
    3.  **结束会议**:
        *   用户点击“结束会议”按钮。
        *   系统弹出一个模态确认框：“您确定要结束本次会议吗？”。
        *   用户点击“确定”。APP调用后端“结束会议”接口（传入`taskId`）。
        *   按钮变为禁用状态，并显示加载提示，文案为：“会议已结束，正在生成纪要...”。
        *   APP收到后端成功响应后，自动跳转到当前会议的详情页面。

*   **验收标准 (Acceptance Criteria)**:
    1.  **Given** 用户在首页, **When** 点击“开始会议”且网络正常, **Then** 按钮变为“结束会议”且会议时长开始计时，界面呈现动态效果。
    2.  **Given** 会议正在进行, **When** WebSocket返回转录数据, **Then** 文本内容必须实时显示在界面上。
    3.  **Given** 用户点击“结束会议”并确认, **When** 后端返回成功, **Then** APP必须自动跳转到会议详情页。

*   **异常流程/错误处理 (Exception Flows / Error Handling)**:
    1.  **场景**: 任何需要调用后端的环节（开始/结束会议）发生网络错误或服务器5xx错误。**处理**: APP应在界面上显示全局提示：“网络错误，请稍后重试。”，流程中断，界面恢复到操作前状态。
    2.  **场景**: 会议中途设备断开网络。**处理**: APP应立即尝试关闭WebSocket连接，并在界面上显示全局提示：“网络连接已断开，转录已停止。”。“结束会议”按钮的功能应变为“会议中断（可能丢失部分数据）”，点击后仍尝试调用后端“结束会议”接口。

---

### 4.2 功能模块二：会议记录查看

#### 4.2.1 用户故事 2.1: 查看会议详情

*   **用户故事 (User Story)**: **作为** 一名企业用户, **我希望** 在会议结束后，能在一个页面集中查看会议的所有信息、播放录音和回顾完整对话, **以便于** 我能快速地找到所需信息。

*   **界面元素与逻辑 (UI Elements & Logic)**:
    1.  **进入方式**: 用户在会议历史列表中点击某一条记录。APP调用后端“获取会议详情”接口（传入`taskId`）。
    2.  **加载状态**: 在等待后端服务返回数据时，页面应显示加载占位符（Shimmer effect）。
    3.  **数据展示**:
        *   **会议标题**: 使用后端返回的`paragraph_title`。
        *   **基本信息区**: 展示后端返回的 **开始时间**、**结束时间**、**总时长**、**参会人数**。
        *   **AI摘要区**: 展示后端返回的`paragraph_summary`。如果无摘要，则此区域不显示。
        *   **音频播放器**: 使用后端返回的`output_mp3_path`作为音源，提供播放/暂停和进度条。
        *   **完整对话记录区**: 列表展示后端返回的`transcriptions.paragraphs`数据，每一条都包含发言人标签和文本内容。
    4.  **状态处理**: 如果后端返回的任务状态是`PROCESSING`，详情页可以显示提示“会议纪要仍在生成中，当前数据可能不完整，请稍后查看”。