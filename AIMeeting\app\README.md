# AI会议记录APP - Flutter客户端

## 📱 项目概述

这是AI会议记录APP的Flutter客户端，采用**无头主机架构**，通过WebView容器托管H5单页应用，实现跨平台的会议记录功能。

## 🏗️ 架构设计

### 无头主机架构
```
Flutter App (无头主机)
    ↓
WebView容器 + 本地Web服务器
    ↓
H5单页应用 (完整功能实现)
```

### 核心组件
- **WebViewContainer**: 全屏WebView容器
- **WebServerService**: 本地HTTP服务器 (127.0.0.1:8080-8090)
- **H5 SPA**: 完整的会议记录界面和交互逻辑

## 🚀 构建和运行

### 环境要求
- Flutter SDK 3.19.6+
- Dart 3.3.4+
- Android SDK (API 21+)
- Android Studio / VS Code

### 安装依赖
```bash
flutter pub get
```

### 运行应用
```bash
# Debug模式
flutter run

# Release模式
flutter run --release

# 指定设备
flutter run -d <device_id>
```

### 构建APK
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# 分架构构建
flutter build apk --split-per-abi
```

## 🔧 配置说明

### 权限配置
应用已配置以下权限：
- `INTERNET`: 网络访问
- `ACCESS_NETWORK_STATE`: 网络状态检测
- `RECORD_AUDIO`: 麦克风录音
- `WAKE_LOCK`: 防止应用后台被杀死

### 网络安全配置
- 允许本地HTTP连接 (127.0.0.1, localhost)
- 支持HTTPS连接
- WebView硬件加速启用

### 应用配置
- 应用名称: "AI会议记录"
- 屏幕方向: 竖屏锁定
- 状态栏: 透明
- 导航栏: 深色主题

## 📁 项目结构

```
app/
├── lib/
│   ├── main.dart                 # 应用入口
│   └── services/
│       └── web_server.dart       # 本地Web服务器
├── assets/                       # H5资源文件
│   ├── index.html               # 主页面
│   ├── css/
│   │   └── styles.css           # 样式文件
│   ├── js/
│   │   └── app.js               # JavaScript逻辑
│   └── webfonts/                # 字体文件
├── android/                     # Android配置
└── pubspec.yaml                 # 依赖配置
```

## 🧪 测试指南

### 功能测试清单

#### 1. 应用启动测试
- [ ] 应用正常启动
- [ ] 本地Web服务器成功启动
- [ ] H5页面正常加载
- [ ] 界面显示完整

#### 2. 权限测试
- [ ] 麦克风权限请求正常
- [ ] 权限被拒绝时显示错误提示
- [ ] 网络权限正常工作

#### 3. 界面交互测试
- [ ] 侧边栏正常展开/收起
- [ ] 搜索功能正常
- [ ] 筛选功能正常
- [ ] 会议列表显示正常
- [ ] 按钮点击响应正常

#### 4. WebView功能测试
- [ ] WebView正常加载H5页面
- [ ] JavaScript执行正常
- [ ] CSS样式正确显示
- [ ] 网络请求正常

#### 5. 本地服务器测试
- [ ] 服务器自动启动
- [ ] 端口自动分配
- [ ] 静态资源正常访问
- [ ] CORS配置正确

### 调试方法

#### 1. 查看日志
```bash
flutter logs
```

#### 2. WebView调试
在Chrome中访问 `chrome://inspect` 可以调试WebView内容

#### 3. 网络调试
检查本地服务器日志，确认资源加载状态

## 🐛 常见问题

### 1. 应用启动失败
**问题**: 应用启动时显示错误
**解决**: 
- 检查Flutter环境配置
- 运行 `flutter doctor` 检查环境
- 确保Android SDK正确安装

### 2. H5页面加载失败
**问题**: WebView显示空白或错误
**解决**:
- 检查assets资源是否正确配置
- 确认本地服务器是否启动成功
- 查看控制台日志

### 3. 权限被拒绝
**问题**: 麦克风权限被拒绝
**解决**:
- 在设备设置中手动授予权限
- 重新安装应用
- 检查AndroidManifest.xml配置

### 4. 网络连接问题
**问题**: 无法连接本地服务器
**解决**:
- 检查网络安全配置
- 确认端口没有被占用
- 重启应用

## 📋 开发注意事项

### 1. 资源更新
修改H5资源后需要：
```bash
flutter clean
flutter pub get
flutter run
```

### 2. 权限变更
修改权限配置后需要重新安装应用：
```bash
flutter clean
flutter run
```

### 3. 调试技巧
- 使用 `print()` 输出调试信息
- 在Chrome中调试WebView内容
- 使用Android Studio的Logcat查看系统日志

## 🔄 版本信息

- **当前版本**: 1.0.0+1
- **Flutter版本**: 3.19.6
- **Dart版本**: 3.3.4
- **最低Android版本**: API 21 (Android 5.0)

## 📞 技术支持

如遇到问题，请检查：
1. Flutter环境是否正确配置
2. 依赖包是否正确安装
3. Android设备是否支持
4. 权限是否正确授予

---

**注意**: 这是MVP版本，专注于核心功能演示。后续版本将添加更多功能和优化。
