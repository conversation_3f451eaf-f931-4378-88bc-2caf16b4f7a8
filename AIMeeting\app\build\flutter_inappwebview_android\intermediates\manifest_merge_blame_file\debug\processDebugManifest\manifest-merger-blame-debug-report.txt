1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pichillilorenzo.flutter_inappwebview_android" >
4
5    <uses-sdk android:minSdkVersion="19" />
6
7    <queries>
7-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:37:5-42:15
8        <intent>
8-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:38:9-41:18
9            <action android:name="android.support.customtabs.action.CustomTabsService" />
9-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:39:13-40:73
9-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:39:21-104
10        </intent>
11    </queries>
12
13    <application>
13-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:4:5-35:19
14        <activity
14-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:5:9-9:140
15            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
15-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:8:13-112
16            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
16-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:9:13-137
17            android:exported="false"
17-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:7:13-37
18            android:theme="@style/AppTheme" />
18-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:6:13-44
19        <activity
19-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:10:9-13:123
20            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
20-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:13:13-120
21            android:exported="false"
21-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:12:13-37
22            android:theme="@style/ThemeTransparent" />
22-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:11:13-52
23        <activity
23-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:14:9-17:117
24            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
24-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:17:13-114
25            android:exported="false"
25-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:16:13-37
26            android:theme="@style/ThemeTransparent" />
26-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:15:13-52
27        <activity
27-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:18:9-22:50
28            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
28-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:21:13-134
29            android:exported="false"
29-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:20:13-37
30            android:launchMode="singleInstance"
30-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:22:13-48
31            android:theme="@style/ThemeTransparent" />
31-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:19:13-52
32        <activity
32-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:23:9-27:50
33            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
33-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:26:13-128
34            android:exported="false"
34-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:25:13-37
35            android:launchMode="singleInstance"
35-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:27:13-48
36            android:theme="@style/ThemeTransparent" />
36-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:24:13-52
37
38        <receiver
38-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:28:9-31:40
39            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
39-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:29:13-119
40            android:enabled="true"
40-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:30:13-35
41            android:exported="false" />
41-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:31:13-37
42
43        <meta-data
43-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:32:9-34:36
44            android:name="io.flutter.embedded_views_preview"
44-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:33:13-61
45            android:value="true" />
45-->G:\Temp\Flutter\.pub-cache\hosted\pub.flutter-io.cn\flutter_inappwebview_android-1.0.13\android\src\main\AndroidManifest.xml:34:13-33
46    </application>
47
48</manifest>
