{"configVersion": 2, "packages": [{"name": "ansicolor", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/ansicolor-2.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "archive", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "characters", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "convert", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "crypto", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "csslib", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "ffi", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter", "rootUri": "file:///D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_inappwebview", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview-6.0.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_android", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.0.13", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_internal_annotations", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_internal_annotations-1.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_ios", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.0.13", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_macos", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.0.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_platform_interface", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_platform_interface-1.0.10", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_web", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.0.8", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_lints", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_native_splash", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_native_splash-2.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_web_plugins", "rootUri": "file:///D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "html", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http_parser", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "js", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "lints", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/lints-2.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.8.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/meta-1.11.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "path", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_android", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "path_provider_foundation", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.3.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_android", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.0.13", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "permission_handler_apple", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.2.3", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "permission_handler_windows", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "platform", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "posix", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/posix-6.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "shelf", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf_static-1.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///D:/Development/SDK/Flutter/flutter_3.19.6/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "typed_data", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.3.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "universal_io", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/universal_io-2.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "vector_math", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "web", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xdg_directories", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///G:/Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "ai_meeting_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.3"}], "generated": "2025-07-28T15:07:28.885003Z", "generator": "pub", "generatorVersion": "3.3.4"}