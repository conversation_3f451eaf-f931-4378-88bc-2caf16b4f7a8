# 系统设计文档：AI会议记录APP (MVP) - V1.0

## 1. 文档概述

*   **关联PRD版本**: V1.0
*   **核心技术目标**: 本文档旨在将PRD V1.0中定义的需求，转化为一个基于 **“阿里云服务-后端-客户端”** 三层架构的清晰、可执行的技术实现方案。本文档是整个开发工作的核心指南。

## 2. 系统架构设计 (System Architecture)

*   **架构风格**: **三层客户端/服务器架构 (3-Tier Client/Server Architecture)**

*   **架构图**:
    ```
    +-----------------+        (HTTPS API)        +------------------------+        (HTTPS API / WebSocket)        +---------------------+
    |                 | <-----------------------> |                        | <-----------------------------------> |                     |
    |  Flutter Client |                           |  Backend Service       |                                       |  Aliyun Realtime    |
    |  (Android)  |                               |  (Python FastAPI)      |                                       |  Meeting Service    |
    |                 | <-----------------------> |                        |                                       |                     |
    +-----------------+                           +-----------+------------+                                       +---------------------+
                                                              |
                                                              | (SQL)
                                                              v
                                                      +----------------+
                                                      |                |
                                                      |  MySQL Database|
                                                      |                |
                                                      +----------------+
    ```

*   **架构文本描述**:
    本系统采用经典的三层架构，将展示、业务逻辑和数据存储完全分离，以实现高内聚、低耦合的设计。

    1.  **客户端 (Flutter Client，位于项目app目录下)**:
        *   **角色**: 作为用户交互的GUI和音频数据的采集点。
        *   **架构风格**: **无头Flutter主机 + 全功能H5单页应用 (Headless Flutter Host + Full-featured H5 SPA)**
        *   **架构描述**:
            客户端采用职责高度分离的混合架构。Flutter应用本身不包含任何可见的UI组件，其核心职责是作为一个稳定高效的后台服务主机。100%的用户界面和交互逻辑都由一个运行在全屏WebView中的H5单页应用（SPA）中来控制。
            1.  **无头Flutter主机 (The Headless Flutter Host)**:
                *   **角色**: 一个不可见的、提供原生能力的后台服务。
                *   **职责**:
                    *   **WebView容器**: 启动后立即加载一个占满全屏的`webview_flutter`组件。
                    *   **本地Web服务器**: 通过`shelf`工具包，在Flutter本地`http://127.0.0.1`上运行一个Web服务器来托管H5应用资源，以规避`file://`协议的安全限制。
            2.  **全功能H5单页应用 (The Full-featured H5 SPA)**:
                *   **角色**: 应用的唯一用户界面和交互逻辑中心。
                *   **职责**:
                    *   **UI渲染**: 负责渲染应用的所有视觉元素，包括侧边栏、主内容区、按钮、列表、表单、播放器等。
                    *   **应用内导航**: 实现一个前端路由器，管理不同视图（主页与详情页）之间的切换，而无需重新加载整个Web页面。
                    *   **状态管理**: 管理所有UI状态，如侧边栏的展开/收起、当前显示的会议信息等。
                    *   **实时数据通信**: 这是**唯一直接与阿里云交互的任务**，在从后端获取到`MeetingJoinUrl`后，通过WebSocket (`wss://`) 直接与阿里AI云端服务进行通信，负责音频流的发送和实时转录结果的接收。
                    *   **与后端服务交互**: 作为所有用户操作的起点，通过HTTP协议调用后端服务API接口来发起所有业务操作（开始/结束/查询会议），同时提供默认认证信息（在请求AUTH头中附加固定值）。

    2.  **后端服务 (Backend Service，位于项目web目录下)**:
        *   **角色**: 系统的核心，承载所有业务逻辑和状态管理。
        *   **职责**:
            *   **API网关**: 提供一套RESTful API供客户端APP调用。
            *   **安全代理**: 封装对上游阿里云服务的所有API调用，安全地管理`AppKey`等敏感凭证。
            *   **生命周期管理器**: 在MySQL数据库中跟踪每个会议任务的状态（`NEW`, `PROCESSING`, `COMPLETED`, `FAILED`）。
            *   **异步任务处理器**: 在会议结束后，启动后台任务，以**每分钟1次**的频率轮询阿里云，直到获取最终结果。
            *   **数据ETL管道**: 从阿里云查询会议结果(JSON格式)，解析并将其中的结构化数据持久化到MySQL的对应表中。

    3.  **数据层 (Data Tier)**:
        *   **角色**: 数据的持久化中心。
        *   **职责**:
            *   **阿里云服务**: 提供核心的AI能力（实时转录、摘要等）。
            *   **MySQL数据库**: 存储自身业务数据，包括会议元数据、处理状态、转录稿、摘要等。表结构遵循 `db.sql` 定义。

## 3. 技术选型 (Technology Stack)

*   **客户端 (Client)**:
    *   **框架**: `Flutter 3.19.6`, `Dart 3.3.4`
    *   **Web服务器**: `shelf`
*   **后端服务 (Backend)**:
    *   **框架**: `Python 3.10.8`, `FastAPI`
    *   **Web服务器**: `Uvicorn`
    *   **数据库ORM**: `SQLAlchemy`
    *   **异步任务**: FastAPI `BackgroundTasks` (用于在会议结束后轮询状态)
*   **数据库 (Database)**:
    *   `MySQL 8.0+`

## 4. 核心系统交互流程 (Core Interaction Flows)

#### **流程一: 创建并开始一个新会议**
1.  **Client**: 用户点击“开始会议”按钮，触发H5中JS的监听事件。
2.  **Client -> Backend**: 在H5页面中通过JS向后端服务发送 `POST /meetings` 请求（携带固定AUTH认证头）。
3.  **Backend**:
    a. 接收请求，验证AUTH头数据。
    b. 调用阿里云SDK(位于aliyun_meeting.py)的`start_realtime_meeting` 方法。
    c. 成功后，将返回的`TaskId`和`MeetingJoinUrl`等信息存入MySQL的`meeting_tasks`表，会议名称默认使用 `会议_YYYY-MM-DD HH:MM` 格式，`task_status`设为`'NEW'`。
    d. 向客户端H5返回 `{ "taskId": "...", "meetingJoinUrl": "..." }`。
4.  **Client**:
    a. H5收到响应，解析出`taskId`和`meetingJoinUrl`并保存。
    b. 切换到“会议中”UI。
    c. 在H5页面中与`meetingJoinUrl`建立WebSocket连接，开始推送音频流。

#### **流程二: 结束会议并启动后端处理**
1.  **Client**: 用户点击“结束会议”按钮，触发H5中JS的监听事件。
2.  **Client -> Backend**: 在H5页面中通过JS向后端服务发送 `POST /meetings/{taskId}/stop` 请求。
3.  **Backend**:
    a. 接收请求，验证AUTH头数据。
    b. 调用阿里云SDK(位于aliyun_meeting.py)的`end_realtime_meeting` 方法。
    c. 将MySQL中对应`taskId`的记录`task_status`更新为`'PROCESSING'`。
    d. **启动一个后台异步任务** (`BackgroundTasks`)，该任务负责轮询此`taskId`的结果。
    e. 向客户端返回 `HTTP 200 OK`。
4.  **Client**: H5收到成功响应后，跳转到会议详情页。

#### **流程三: 后端异步轮询与数据持久化**
1.  **Backend (Background Task)**:
    a. 任务启动，进入一个循环，第一次在**5秒**后执行，之后每隔**70秒**执行一次。
    b. 调用阿里云SDK(位于aliyun_meeting.py)的`get_realtime_meeting` 方法查询`taskId`的状态。
    c. **如果** `TaskStatus` 仍是 `ONGOING`或因为网络等原因查询失败，等待下一次轮询。
    d. **如果** `TaskStatus` 变为 `COMPLETED`:
        i.   从响应中提取`Result`对象里的各类信息对应的URL。
        ii.  根据每个URL分别**请求** `Transcription`, `Summarization`, `MeetingAssistance` 的地址。
        iii. **解析**响应的JSON数据。
        iv.  将解析出的数据分别**插入**到MySQL的`transcriptions`, `summarizations`, `meeting_assistances`表中，并与主表`meeting_tasks`建立外键关联。
        v.   更新`meeting_tasks`表，将`task_status`设为`'COMPLETED'`，并填充`output_mp3_path`等最终URL。
        vi.  **结束轮询循环**。

## 5. 后端服务API接口设计 (Backend API Specification)

*   **Base URL**: `http://[ip:port]/api/v1`
*   **认证**: 所有请求必须在HTTP 的Auth Header中包含固定值 `Authorization: 6L+H5b6X6aOe5b+r5o6g6L+H55qE77yM5byA5LqG5Liq77yM55qE5Lik5Liqdm1mbHM7bWZsbU06TE1PXiZUJjg3NzXliIbmrrXml7Y=`。
*   **数据规范**: 全部采用json格式交互。响应固定为code(200表示处理成功)、msg(如果处理失败则需要添加失败信息描述)与data(如果处理成功则可能包含的业务数据)这3个参数。 

---
#### **1. 创建实时会议任务**
*   **Endpoint**: `POST /meetings`
*   **描述**: 初始化一个新会议，并获取用于推送音频的WebSocket地址。
*   **请求体**: 无
*   **成功响应**:
    ```json
    {
        "code": 200,
        "taskId": "...",  // 从阿里云获取的任务ID
        "meetingJoinUrl": "wss://tingwu-realtime-cn-beijing.aliyuncs.com/api/ws/v1?..."// 从阿里云获取的websocket地址
    }
    ```
*   **错误响应**:
    ```json
    { "code": 400, "msg": "验证失败" }
    ```

---
#### **2. 结束实时会议任务**
*   **Endpoint**: `POST /meetings/{task_id}/stop`
*   **描述**: 通知服务器指定会议的音频流已推送完毕，可以开始进行后续处理。
*   **URL参数**:
    *   `task_id` (string, required): 要结束的任务ID。
*   **请求体**: 无
*   **成功响应**:
    ```json
    { "code": 200 }
    ```

---
#### **3. 获取会议任务结果**
*   **Endpoint**: `GET /meetings/{task_id}`
*   **描述**: 获取指定会议的完整结果，包括元数据、摘要、转录内容等。
*   **URL参数**:
    *   `task_id` (string, required): 要查询的任务ID。
*   **成功响应**:
    ```json
    {
        "code": 200,
        "taskId": "...",
        "taskStatus": "COMPLETED", // 'NEW', 'PROCESSING', 'COMPLETED', 'FAILED'
        "createdAt": "...",
        "audioFileUrl": "https://...",
        "durationInSeconds": 71,
        "summary": { // 来自 summarizations 表
            "title": "生气与食欲的幽默探讨",
            "paragraph": "在一段对话中...",
            "conversational": [
                { "speakerId": "1", "speakerName": "发言人1", "summary": "首先关心地询问..." },
                { "speakerId": "2", "speakerName": "发言人2", "summary": "他以一种幽默而又..." }
            ]
        },
        "transcripts": [ // 来自 transcriptions 表
            {
                "speakerId": "1",
                "text": "今天你吃饭了吗？",
                "startTimeMs": 1230,
                "endTimeMs": 2950
            },
            {
                "speakerId": "2",
                "text": "不想吃。",
                "startTimeMs": 8590,
                "endTimeMs": 9570
            }
        ],
        "assistance": { // 来自 meeting_assistances 表
            "keywords": ["吃饭", "河豚"],
            "classifications": { "Lecture": 0.62, "Interview": 0.24, "Meeting": 0.13 }
        }
    }
    ```

---
#### **4. 查询实时会议任务列表**
*   **Endpoint**: `GET /meetings`
*   **描述**: 获取会议历史记录列表，按创建时间倒序排列。
*   **Query参数**:
    *   `skip` (int, optional, default: 0): 分页跳过数量。
    *   `limit` (int, optional, default: 20): 每页数量。
*   **成功响应**:
    ```json
    {
        "code": 200,
        "data": [
            {
                "taskId": "b047e09c9dfe4029991f3cdaef521048",
                "title": "生气与食欲的幽默探讨",
                "taskStatus": "COMPLETED",
                "createdAt": "2025-07-23T10:00:00Z",
                "durationInSeconds": 71
            },
            {
                "taskId": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
                "title": "会议 - 2025-07-22 15:30",
                "taskStatus": "PROCESSING",
                "createdAt": "2025-07-22T15:30:00Z",
                "durationInSeconds": null
            }
        ]
    }
    ```

## 6. 数据库设计 (Database Schema)

*   **数据库**: MySQL 8.0+
*   **Schema定义**: 本项目将严格遵循 `db.sql` 文件中定义的MySQL表结构，包括 `meeting_tasks`, `transcriptions`, `summarizations`, `meeting_assistances` 四张表及其字段和外键约束。

## 7. 项目结构

*   **docs**: 项目文档，包括需求文档、系统设计文档、数据库设计文档以及API接口标准
*   **app**: Flutter 安卓客户端项目
*   **web**: FastAPI 后端Web服务